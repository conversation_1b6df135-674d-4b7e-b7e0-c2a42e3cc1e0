package tool;

import com.teamdev.jxbrowser.deps.com.google.common.base.Preconditions;
import com.teamdev.jxbrowser.logging.Logger;
import com.teamdev.jxbrowser.os.Environment;
import view.Dashboard;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.function.Consumer;

public final class SevenZip {
    private static final int SUCCESS_EXIT_CODE = 0;
    private static final int WARNING_EXIT_CODE = 1;
    private final Path executable;

    static String executableName() {
        if (Environment.isWindows()) {
            return "7zr.exe";
        } else if (Environment.isMac()) {
            return "7zr-mac";
        } else {
            throw new IllegalStateException("Unsupported operating system");
        }
    }

    private SevenZip(Path executable) {
        this.executable = executable;
    }

    public static SevenZip create() throws Exception {
        String property = System.getProperty("user.dir");
        return new SevenZip(Paths.get(property).resolve(".bin").resolve(executableName())).verifyExec();
    }

    // 验证平台压缩命令文件
    public SevenZip verifyExec() throws IOException {
        Preconditions.checkNotNull(executable);
        if (!new File(executable.toString()).exists()) {
            SevenZip.createExec(executable);
        }
        return this;
    }

    // 移动平台压缩命令文件
    static void createExec(Path target) throws IOException {
        // 获取文件
        InputStream systemResourceAsStream = SevenZip.class.getResourceAsStream("/" + executableName());
        // 将文件写入到本地
        File outputFile = new File(target.toString());
        boolean ignore = outputFile.getParentFile().mkdirs();
        ignore = outputFile.createNewFile();
        outPutFileToTargetDir(systemResourceAsStream, outputFile);
        ignore = outputFile.setExecutable(true);
        ignore = outputFile.setReadable(true);
    }

    /**
     * 输出文件到指定目录
     */
    public static void outPutFileToTargetDir(InputStream inputStream, File outputFile) {
        try {
            OutputStream outputStream = Files.newOutputStream(outputFile.toPath());
            outPutFile(inputStream, outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void outPutFile(InputStream input, OutputStream output) throws IOException {
        try {
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            input.close();
            output.close();
        }
    }

    // 解压
    public void decompress(Path archive, Path destinationDir) throws Exception {
        String executablePath = this.executable.toString();
        String destinationDirPath = destinationDir.toString();
        String archivePath = archive.toString();
        String[] args = new String[]{executablePath, "-aoa", "-o" + destinationDirPath, "x", archivePath};
        Process xzProcess = Runtime.getRuntime().exec(args);
        this.startLogging(xzProcess);
        this.waitFor(xzProcess);
    }

    // 压缩
    public void compress(Path targetDir, Path archive) throws Exception {
        String executablePath = this.executable.toString();
        String target = targetDir.toString();
        String source = archive.toString();
        String[] args = new String[]{executablePath, "a", target, source};
        Process xzProcess = Runtime.getRuntime().exec(args);
        this.startLogging(xzProcess);
        this.waitFor(xzProcess);
    }

    private void waitFor(Process xzProcess) throws Exception {
        int exitCode = xzProcess.waitFor();
        if (exitCode != 0 && exitCode != 1) {
            throw new Exception("7z exited with an error. Exit code: " + exitCode);
        }
    }

    private void startLogging(Process process) {
        this.startLogging("out", process.getInputStream(), Logger::info);
        this.startLogging("error", process.getErrorStream(), Logger::debug);
    }

    private void startLogging(String logName, InputStream inputStream, Consumer<String> logger) {
        Thread processConsoleThread = new Thread(() -> {
            try {
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                BufferedReader input = new BufferedReader(inputStreamReader);
                StringBuilder builder = new StringBuilder();

                String line;
                while ((line = input.readLine()) != null) {
                    builder.append(line).append("\n");
                }

                logger.accept(builder.toString());
                input.close();
            } catch (IOException var6) {
                Logger.error("Failed to log process output.", var6);
            }

        }, String.format("XZ Logger (%s)", logName));
        processConsoleThread.setDaemon(true);
        processConsoleThread.start();
    }

}
