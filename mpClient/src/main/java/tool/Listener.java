package tool;

import env.Global;
import view.Game;

public class Listener extends Thread {

    public Game game;
    public boolean run = true;

    public Listener(Game game) {
        this.game = game;
    }

    protected long sTime = 0;

    @Override
    public void run() {
        this.sTime = System.currentTimeMillis();
        this.listen();
    }


    // 监听游戏页面
    public void listen() {
        while (run) {
            SystemUtil.delay(200);
            run = false;
        }
        this.doEnterGamePage(game.url);
    }

    public void doEnterGamePage(String url) {
        if (!Global.remote_http) {
            url = url.replace("https://worldh5.gamehz.cn/version/world/publish/channel/res/index.html", "http://localhost:5174/index.html");
        } else {
            url = url.replace("https://worldh5.gamehz.cn/version/world/publish/channel/res/index.html", "game://game/index.html");
        }
        if (!game.browser.url().equals(url)) {
            System.out.println("doEnterGamePage:" + url);
            game.browser.navigation().loadUrl(url);
        }
    }

}
