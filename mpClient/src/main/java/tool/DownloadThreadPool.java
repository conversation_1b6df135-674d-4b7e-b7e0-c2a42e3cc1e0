package tool;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.*;
import java.util.function.BiConsumer;

/**
 * 文件下载线程池管理器
 * 提供异步文件下载功能，支持回调通知
 */
public class DownloadThreadPool {
    private static final int CORE_POOL_SIZE = 4;
    private static final int MAXIMUM_POOL_SIZE = 8;
    private static final long KEEP_ALIVE_TIME = 60L;
    
    private final ThreadPoolExecutor executor;
    private static DownloadThreadPool instance;
    
    private DownloadThreadPool() {
        executor = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAXIMUM_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactory() {
                private int counter = 0;
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "DownloadThread-" + (++counter));
                    thread.setDaemon(true);
                    return thread;
                }
            }
        );
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized DownloadThreadPool getInstance() {
        if (instance == null) {
            instance = new DownloadThreadPool();
        }
        return instance;
    }
    
    /**
     * 提交下载任务
     * @param unique 唯一标识符
     * @param url 下载URL
     * @param callback 回调函数，参数为(unique, result)，result为空字符串表示失败
     */
    public void submitDownload(String unique, String url, BiConsumer<String, String> callback) {
        executor.submit(() -> {
            try {
                String result = downloadFile(url);
                callback.accept(unique, result);
            } catch (Exception e) {
                System.err.println("下载任务执行失败: " + e.getMessage());
                e.printStackTrace();
                callback.accept(unique, "");
            }
        });
    }
    
    /**
     * 执行文件下载
     * @param url 下载URL
     * @return 成功返回本地文件路径，失败返回空字符串
     */
    private String downloadFile(String url) {
        try {
            String property = System.getProperty("user.dir");
            Path basePath = Paths.get(property).resolve("src/test/remote/");
            
            // 从URL中提取路径部分（去掉域名）
            String relativePath = extractPathFromUrl(url);
            if (relativePath == null || relativePath.isEmpty()) {
                System.err.println("无法从URL中提取有效路径: " + url);
                return "";
            }
            
            int argIndex = relativePath.indexOf("?");
            if (argIndex != -1) {
                relativePath = relativePath.substring(0, argIndex);
            }
            
            // 构建完整的本地文件路径
            Path targetPath = basePath.resolve(relativePath);
            File targetFile = targetPath.toFile();
            
            String link = targetFile.getAbsolutePath()
                    .replace(File.separator, "/")  // 统一使用正斜杠
                    .replace(System.getProperty("user.dir").replace("\\", "/"), "");
            
            // 如果文件已存在，直接返回成功
            if (targetFile.exists()) {
                System.out.println("文件已存在，跳过下载: " + targetPath);
                return link;
            }
            
            // 确保父目录存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 执行下载
            System.out.println("开始下载文件: " + url + " -> " + targetPath);
            if (downloadFileFromUrl(url, targetFile)) {
                return link;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
    
    /**
     * 从URL中提取路径部分
     */
    private String extractPathFromUrl(String url) {
        try {
            // 移除协议部分
            if (url.startsWith("http://") || url.startsWith("https://")) {
                int protocolEnd = url.indexOf("://") + 3;
                String withoutProtocol = url.substring(protocolEnd);
                
                // 找到第一个斜杠，后面就是路径部分
                int firstSlash = withoutProtocol.indexOf('/');
                if (firstSlash != -1) {
                    return withoutProtocol.substring(firstSlash + 1);
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 从URL下载文件到本地
     */
    private boolean downloadFileFromUrl(String url, File targetFile) {
        try {
            URL fileUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) fileUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            connection.connect();
            
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                try (InputStream inputStream = connection.getInputStream();
                     FileOutputStream outputStream = new FileOutputStream(targetFile)) {
                    
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }
                System.out.println("文件下载成功: " + targetFile.getAbsolutePath());
                return true;
            } else {
                System.err.println("下载失败，HTTP状态码: " + responseCode);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取线程池状态信息
     */
    public String getPoolStatus() {
        return String.format("Pool Status - Active: %d, Queue: %d, Completed: %d", 
            executor.getActiveCount(), 
            executor.getQueue().size(), 
            executor.getCompletedTaskCount());
    }
    
    /**
     * 关闭线程池
     */
    public void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
