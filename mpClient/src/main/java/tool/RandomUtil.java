package tool;

import java.util.LinkedList;
import java.util.List;
import java.util.Random;

/**
 * @Author: husong
 * @Date: 2021/10/14 19:13
 * @Description: 随机类
 */
public class RandomUtil {
    private static Random random = new Random();

    static {
        random.setSeed(System.currentTimeMillis() * random.hashCode() * random.getClass().hashCode());
    }

    public static int random(int max) {
        return random.nextInt(max);
    }

    public static int random(int min, int max) {
        if (max - min <= 0) {
            return min;
        }
        return min + random.nextInt(max - min + 1);
    }

    /**
     * 根据总几率计算返回
     * 总几率就是probs值的合
     * [1,2,3,4,5] 总几率是15
     *
     * @param probs
     * @return
     */
    public static int randomIndex(List<Integer> probs) {
        try {
            LinkedList<Integer> newprobs = new LinkedList<Integer>();
            for (int i = 0; i < probs.size(); i++) {
                if (i == 0) {
                    newprobs.add(probs.get(i));
                } else {
                    newprobs.add(newprobs.get(i - 1) + probs.get(i));
                }
            }
            if (newprobs.size() <= 0) {
                return -1;
            }
            int last = newprobs.getLast();
            if (last == 0) {
                return -1;
            }
            int random = random(last);
            for (int i = 0; i < newprobs.size(); i++) {
                int value = newprobs.get(i);
                if (value > random) {
                    return i;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }
}
