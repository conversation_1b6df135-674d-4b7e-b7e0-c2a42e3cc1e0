package tool;

import com.google.common.primitives.Bytes;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.rmi.server.ExportException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Scanner;


public class SystemUtil {
    public static final long SECOND = 1000;
    public static final long MINUTE = 60 * 1000;
    public static final long HOUR = 60 * 60 * 1000;
    public static final long DAY = 12 * 60 * 60 * 1000;
    public static final int MIN_DELAY = 900;
    public static final int MAX_DELAY = 2000;

    public static boolean isNotEmpty(Object object) {
        return object != null;
    }

    public static boolean isEmpty(Object object) {
        return object == null;
    }

    public static void delay(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            Thread.currentThread().stop();
        }
    }

    public static void delayNoCatch(long time) throws InterruptedException {
        Thread.sleep(time);
    }

    public static void randomDelay() throws InterruptedException {
        randomDelay(MIN_DELAY, MAX_DELAY);
    }

    public static void randomDelay(int min, int max) throws InterruptedException {
        int random = RandomUtil.random(min, max);
        System.out.println(random);
        Thread.sleep(random);
    }

    /**
     * 用来输出jar里面的文件到指定目录
     *
     * @param input
     * @param output
     * @throws IOException
     */
    public static void outPutFile(InputStream input, OutputStream output) throws IOException {
        if (input == null || output == null) {
            throw new ExportException("input or output Stream in Null.");
        }
        try {
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            input.close();
            output.close();
        }
    }

    /**
     * 输出文件到jre目录
     */
    public static void outPutFileToSystemDir(InputStream inputStream, String fileName) {
        String outputPath = System.getProperty("user.dir")
                + File.separator + "bin" + File.separator + "config" + File.separator + fileName;
        File outputFile = new File(outputPath);
        outPutFileToTargetDir(inputStream, outputFile);
    }

    /**
     * 输出文件到指定目录
     */
    public static void outPutFileToTargetDir(InputStream inputStream, File outputFile) {
        try {
            OutputStream outputStream = new FileOutputStream(outputFile);
            outPutFile(inputStream, outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String readFromFile(String path) {
        return readFromFile(new File(path));
    }

    //读文件
    public static String readFromFile(File file) {
        if (!file.exists()) {
            return "";
        }
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(file);
            List<Byte> iis = new ArrayList<>();
            int len = 0, temp = 0;
            while ((temp = inputStream.read()) != -1) {
                iis.add((byte) (temp));
                len++;
            }
            byte[] bytes = Bytes.toArray(iis);
            inputStream.close();
            return new String(bytes, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    // 获取网卡地址
    public static String getMacAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    StringBuilder macAddress = new StringBuilder();
                    for (byte b : mac) {
                        macAddress.append(String.format("%02X:", b));
                    }
                    if (macAddress.length() > 0) {
                        macAddress.deleteCharAt(macAddress.length() - 1);
                    }
                    return macAddress.toString();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取硬盘序列化
    public static String getHardDiskSerialNumber() {
        String os = System.getProperty("os.name").toLowerCase();
        try {
            if (os.contains("win")) {
                String command = "wmic diskdrive get serialnumber";
                Process process = Runtime.getRuntime().exec(command);
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                String serial = scanner.next();
                scanner.close();
                return serial;
            } else if (os.contains("mac") || os.contains("nix") || os.contains("nux") || os.contains("mac os x")) {
                String command = "ioreg -rd1 -c IOPlatformExpertDevice | awk '/IOPlatformSerialNumber/ {print $3;}'";
                Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", command});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                String serial = scanner.next();
                scanner.close();
                return serial;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取cpu序列号
    public static String getCpuSerial() {
        String serial = null;
        try {
            OperatingSystemMXBean operatingSystemMXBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
            String os = operatingSystemMXBean.getName();
            if (os.contains("Windows")) {
                Process process = Runtime.getRuntime().exec(new String[]{"wmic", "cpu", "get", "ProcessorId"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                scanner.next();
                serial = scanner.next();
                scanner.close();
            } else if (os.contains("Mac")) {
                Process process = Runtime.getRuntime().exec(new String[]{"/usr/sbin/system_profiler", "SPHardwareDataType"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNext()) {
                    String line = scanner.nextLine().trim();
                    if (line.startsWith("Serial Number")) {
                        serial = line.split(":")[1].trim();
                        break;
                    }
                }
                scanner.close();
            } else if (os.contains("Linux")) {
                Process process = Runtime.getRuntime().exec(new String[]{"dmidecode", "-t", "system"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNext()) {
                    String line = scanner.nextLine().trim();
                    if (line.startsWith("Serial Number")) {
                        serial = line.split(":")[1].trim();
                        break;
                    }
                }
                scanner.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return serial;
    }

}
