package net.server;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import mgr.BrowserManager;
import tool.PortTest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.RandomAccessFile;
import java.net.InetSocketAddress;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;

public class Server {
    public static final String SERVER_HOST = "127.0.0.1";
    private int SERVER_PORT = 0;

    private FileLock lock;
    private File file;
    private FileChannel channel;

    public void run() {
        w();
        if (SERVER_PORT == 0) {
            System.out.println("端口分配失败.");
            System.exit(-1);
        }
        NioEventLoopGroup boss = new NioEventLoopGroup();
        NioEventLoopGroup worker = new NioEventLoopGroup();
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(boss, worker)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 1024)
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                .childHandler(new ServerChannelInitializer());
        try {
            ChannelFuture future = bootstrap.bind(new InetSocketAddress(SERVER_HOST, SERVER_PORT)).sync();
            //System.out.println("Server running, port:" + SERVER_PORT);
            future.channel().closeFuture().sync();
        } catch (Exception e) {
            System.exit(-1);
        } finally {
            boss.shutdownGracefully();
            worker.shutdownGracefully();
        }

    }

    private void w() {
        try {
            int port = 26342;
            for (int i = port; i < 27000; i++) {
                if (!PortTest.isHostConnectable(i)) {
                    port = i;
                    break;
                }
            }
            this.file = BrowserManager.getUserDataDir().resolve("lock." + port).toFile();
            File parentFile = file.getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            if (!file.exists()) {
                if (!file.createNewFile()) {
                    // 为什么创建失败？
                }
            }
            channel = new RandomAccessFile(file, "rw").getChannel();
            lock = channel.tryLock();
            if (lock == null) {
                System.exit(0);
            }
            this.SERVER_PORT = port;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void release() {
        try {
            if (this.lock != null && this.lock.isValid()) {
                this.lock.release();
            }
            this.channel.close();
            this.file.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static int getPort() {
        try {
            Path userDataDir = BrowserManager.getUserDataDir();
            File userDataDirFile = userDataDir.toFile();
            if (!userDataDirFile.exists()) {
                return 0;
            }
            File[] files = userDataDirFile.listFiles();
            assert files != null;
            for (File file : files) {
                if (file.getName().startsWith("lock.")) {
                    String port = file.getName().substring(5);
                    return Integer.parseInt(port);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

}
