package launcher;


import javafx.application.Application;
import net.NetworkManager;

/**
 * 控制面板启动器
 */
public class Dashboard implements LauncherBase {


    @Override
    public void launch(String[] args) {
        // 控制启动实例
        if (NetworkManager.getMe().isNettyServerRunning()) {
            new Thread(() -> NetworkManager.getMe().startAsClient()).start();
        } else {
            new Thread(() -> NetworkManager.getMe().startAsServer()).start();
        }
        Application.launch(view.Dashboard.class, args);
    }


}
