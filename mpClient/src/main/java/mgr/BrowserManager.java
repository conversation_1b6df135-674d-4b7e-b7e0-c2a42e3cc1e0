package mgr;

import com.teamdev.jxbrowser.engine.*;
import com.teamdev.jxbrowser.engine.event.EngineClosed;
import com.teamdev.jxbrowser.engine.event.EngineCrashed;
import com.teamdev.jxbrowser.net.Scheme;
import com.teamdev.jxbrowser.os.Environment;
import com.teamdev.jxbrowser.permission.PermissionType;
import com.teamdev.jxbrowser.permission.callback.PermissionsCallback;
import com.teamdev.jxbrowser.permission.callback.RequestPermissionCallback;
import com.teamdev.jxbrowser.zoom.ZoomLevel;
import env.Global;
import tool.Interceptor;
import tool.PortTest;
import tool.Schema;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Random;

public class BrowserManager {
    private static final String key = "6P835FT5HAV2EG8VQ5ZBIC76PR2YH2T40MCSXZ361FBESJ24DO3U9RRANTA2PFUWI2NP";
    private static int debugPort = 54001;
    private final static int MAX = 65535;
    private static EngineOptions engineOptions;
    private static Engine engine;
    public static String dir = "";

    public static void initEngineOptions() {
        try {
            dir = System.getProperty("user.dir");
            EngineOptions.Builder options = EngineOptions
                    .newBuilder(RenderingMode.HARDWARE_ACCELERATED)
                    .licenseKey(key)
                    .allowFileAccessFromFiles()
                    .disableWebSecurity()// 禁用同源警告
                    .language(Language.CHINESE)
                    .passwordStore(PasswordStore.AUTO)
                    // 5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1
                    // Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36
                    // –process-per-tab –site-per-process --in-process-plugins --single-process
                    // Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
                    .userDataDir(getUserDataDir())
                    .chromiumDir(Paths.get(dir).resolve(".chromium"))
                    .addSwitch("--js-flags=--expose-gc")
                    .addSwitch("--disable-desktop-notifications  --disable-speech-input")
                    .addSwitch("–process-per-tab  --in-process-plugins --single-process")
                    .addScheme(Scheme.of("game"), new Schema("game"))
                    .addScheme(Scheme.HTTP, new Interceptor())
                    .addScheme(Scheme.HTTPS, new Interceptor());
            if (Environment.isMac()) {
                options.userAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            } else if (Environment.isWindows()) {
                options.userAgent("Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            }


            if (Global.debug) {
                int port = randomRemoteDebugPort();
                options.remoteDebuggingPort(port);
                System.out.println("引擎开启调试，端口: " + port);
            }
            engineOptions = options.build();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建浏览器引擎
     */
    public static void createEngine() {
        engine = Engine.newInstance(engineOptions);
        // 默认设置成缩放100
        engine.zoomLevels().defaultLevel(ZoomLevel.P_100);

        engine.on(EngineClosed.class, event -> System.out.println("Engine closed"));
        engine.on(EngineCrashed.class, event -> System.out.println("Engine crashed" + event.exitCode()));
        engine.permissions().set(RequestPermissionCallback.class, (params, tell) -> {
            PermissionType type = params.permissionType();
            if (type == PermissionType.CLIPBOARD_READ_WRITE) {
                tell.grant();
            } else if (type == PermissionType.NOTIFICATIONS) {
                tell.grant();
            } else {
                tell.deny();
            }
        });
    }

    public static Engine getEngine() {
        return engine;
    }

    /**
     * 获取用户数据目录 user.home/AppData/Local/Temp/UserData_xxx
     */
    public static Path getUserDataDir() throws IOException {
        return Paths.get(dir).resolve(".browser");
    }

    // 资源缓存目录
    public static Path getCacheDir() {
        return Paths.get(dir).resolve(".cache");
    }

    /**
     * 用于随机一个可用的远程调试端口
     */
    protected static int randomRemoteDebugPort() {
//        Random random = new Random();
//        int port = random.nextInt(MAX - debugPort - 1);
//        if (!PortTest.isHostConnectable(port)) {
//            return port;
//        }
//        return randomRemoteDebugPort();

        for (int i = debugPort; i < MAX; i++) {
            if (!PortTest.isHostConnectable(i)) {
                return i;
            }
        }
        return 0;
    }

}
