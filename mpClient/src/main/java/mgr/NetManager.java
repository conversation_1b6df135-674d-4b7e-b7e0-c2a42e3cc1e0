package mgr;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class NetManager {

    // 发起http请求  返回服务器响应输入流
    public static InputStream connect(String _url) {
        HttpURLConnection connection = null;
        try {
            // 创建远程url连接对象
            URL url = new URL(_url);
            // 通过远程url连接对象打开一个连接，强转成httpURLConnection类
            connection = (HttpURLConnection) url.openConnection();
            // 设置连接方式：get
            connection.setRequestMethod("GET");
            // 设置连接主机服务器的超时时间：15000毫秒
            connection.setConnectTimeout(15000);
            // 设置读取远程返回的数据时间：60000毫秒
            connection.setReadTimeout(60000);
            // 发送请求
            connection.connect();
            // 通过connection连接，获取输入流
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                return connection.getInputStream();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String reqString(String _url) {
        InputStream connect = NetManager.connect(_url);
        if (connect == null) {
            return "";
        }
        String result = "";
        // 封装输入流is，并指定字符集
        BufferedReader reader = new BufferedReader(new InputStreamReader(connect, StandardCharsets.UTF_8));
        try {
            // 存放数据
            StringBuilder buffer = new StringBuilder();
            String temp = null;
            while ((temp = reader.readLine()) != null) {
                buffer.append(temp);
                buffer.append("\r\n");
            }
            result = buffer.toString();
            reader.close();
            connect.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

}
