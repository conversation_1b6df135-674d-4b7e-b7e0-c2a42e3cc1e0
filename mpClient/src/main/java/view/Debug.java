package view;

import javafx.stage.WindowEvent;

public class Debug extends Dashboard {
    private Base base;

    public Debug(String url, Base base) {
        super();
        this.base = base;
        this.width = base.stage.widthProperty().doubleValue();
        this.height = base.stage.heightProperty().doubleValue();
        this.initX = base.stage.getX();
        this.initY = base.stage.getY();
        this.title = "调试窗口";
        if (base instanceof Game) {
            this.id = base.getId();
            this.title = base.stage.getTitle() + "の调试窗口";
        }
        this.url = url;
        this.proName = "debug_" + base.getId();
        this.debug = false;
        this.resizeable = true;
    }

    @Override
    public void createBrowser() {
        super.createBrowser();
    }

    @Override
    public void onClose(WindowEvent event) {
        this.setPositionOnClose();
        this.setSceneSizeOnClose();
        if (this.browser != null) {
            this.browser.close();
        }
        this.stage.close();
        this.base.clearDebug();
    }

    @Override
    public void initEvt() {
        // do nothing
        super.initEvt();
    }


    @Override
    public void onKeyEvent(String key) {

    }

}
