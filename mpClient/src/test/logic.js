console.log("logic.js");

(async function () {
    const sid = setInterval(() => {
        if (loadGameScript && window.REQ_JAVA_LOAD_LOGIC != null) {
            window.REQ_JAVA_LOAD_LOGIC();
        }
        const moduleCache = window.moduleCache;
        if (!moduleCache) return;
        const mod = moduleCache.get("lib/hdsdk");
        if (!mod) return;
        const i = moduleCache.get("lib/dn_sdk_minigame_1.5.3");
        const n = {
            default: moduleCache.get("lib/gravityengine.mg.wx.min")
        };
        mod["login"] = function (e) {
            const t = this;
            t.init();
            const handle = function (o) {
                if (0 == o.status) {
                    t.uid = o.result.uid,
                        t.token = o.result.token,
                        t.openId = o.result.openid,
                        t.unionid = o.result.unionid,
                        t.username = o.result.username,
                        t.userInfo = o.result;
                    var a = o.result.platformid
                        , d = o.result.register
                        , l = o.result.session_key;
                    t.video_ad_id && wx.setStorageSync("partner_vedio_ad_id", t.video_ad_id),
                        console.log("hd login", o),
                        wx.setStorage({
                            key: "hd_access_token",
                            data: t.token
                        }),
                        t.getAdyStrategy();
                    var c = 1;
                    if (console.log("标记首次天数: " + c),
                        "{}" !== JSON.stringify(t.hd_adcode1) && t.request("user/loginext/mini", s, (function (e) {
                            console.log("wx nextlogin", e)
                        }
                        )),
                        t.wxAction_id) {
                        var u = new i.SDK({
                            user_action_set_id: t.wxAction_id,
                            secret_key: t.wxSecret_key,
                            appid: t.wxAppid
                        });
                        i.SDK.setDebug(!0),
                            u.onAppStart(),
                            u.setOpenId(t.openId + ""),
                            1 == d && u.onRegister(),
                            t.dnsdk = u,
                            t.shareInit()
                    }
                    if (t.gekey) {
                        var h = {
                            accessToken: t.gekey,
                            clientId: t.openId,
                            autoTrack: {
                                appLaunch: !0,
                                appShow: !0,
                                appHide: !0
                            },
                            name: "ge"
                        }
                            , g = new n.default(h);
                        g.setupAndStart(),
                            t.ge = g,
                            g.initialize({
                                name: t.username,
                                version: 1,
                                openid: t.openId,
                                enable_sync_attribution: !1
                            }).then((function (e) {
                                console.log("initialize success " + e)
                            }
                            )).catch((function (e) {
                                console.log("initialize failed, error is " + e)
                            }
                            )),
                            setTimeout((function () {
                                0 == d || g.registerEvent()
                            }
                            ), 5e3)
                    }
                    setTimeout((function () {
                        t.initquery(),
                            "{}" === t.hd_adcode1 && t.request("user/loginext/mini", s, (function (e) {
                                e.status
                            }
                            ))
                    }
                    ), 5e3),
                        e && e({
                            state_code: 1,
                            msg: "登录成功",
                            data: {
                                uid: t.uid,
                                token: t.token,
                                session_id: t.openId,
                                session_key: l,
                                unionid: t.unionid,
                                platformid: a
                            }
                        }),
                        o.result.share_title && o.result.share_image && (t.shareTitle = o.result.share_title,
                            t.shareImg = o.result.share_image),
                        1 == o.result.shift && o.result.shift_url && wx.showModal({
                            title: "福利领取，复制浏览器打开",
                            content: o.result.shift_url,
                            showCancel: !0,
                            confirmText: "复制文本",
                            success: function (e) {
                                e.confirm && wx.setClipboardData({
                                    data: o.result.shift_url,
                                    success: function (e) {
                                        wx.getClipboardData({
                                            success: function (e) {
                                                console.log(e.data)
                                            }
                                        })
                                    }
                                })
                            }
                        })
                } else
                    wx.showToast({
                        title: "登录失败！请联系客服",
                        icon: "fail",
                        duration: 2e3
                    }),
                        e && e({
                            state_code: -1,
                            msg: "登录失败",
                            data: {}
                        })
            };
            handle({
                "status": 0,
                "info": "成功",
                "result": {
                    "notice_title": null,
                    "attestation": 0,
                    "unionid": "omfgwwwqoYJgB0Ioklc9k_qCYfqY",
                    "notice_content": null,
                    "openid": "oGuju60GOOc5Ier4mfB-3ZNMrimc",
                    "shift": 0,
                    "share_image": "https://cdn.hardtime.cn/icon/wx0c747e2a0ec57737_1.jpg",
                    "platformid": "wx",
                    "type": 0,
                    "notice_stopdate": null,
                    "token": "108d9db5c00e4c7f95970c7c2e11cc93",
                    "uid": 5940572,
                    "share_title": "上马不喊话 三开战猎萨！",
                    "phone": null,
                    "shift_url": "",
                    "nickname": "游客5940572",
                    "name": "",
                    "session_key": "2RixxMwPb19nuzpoUowrlA==",
                    "account": "2RixxMwPb19nuzpoUowrlA==",
                    "age": "",
                    "username": "游客5940572",
                    "register": 0
                },
                "result2": null
            })
        };
        clearInterval(sid);
    }, 50)
})();

const delay = async function (time) {
    return new Promise(resolve => setTimeout(() => {
        resolve(0)
    }, time))
};
// 监听SystemJs导出
const listenExport = async () => {
    return new Promise((resolve, reject) => {
        const sid = setInterval(() => {
            if (typeof System == 'undefined') return;
            if (typeof System.registerRegistry == 'undefined') return;
            const keys = Object.keys(System.registerRegistry);
            const key = keys.find(key => key.includes('AIManager.ts'));
            if (!key) return;
            clearInterval(sid);
            resolve(key);
        }, 50);
    });
};
const makeExport = async () => {
    window.clazz = {};
    return new Promise((resolve, reject) => {
        const sid = setInterval(() => {
            const symbols = Object.getOwnPropertySymbols(System);
            if (!symbols || !symbols.length) return;
            const value = System[symbols[0]];
            Object.keys(value).forEach(key => {
                const data = value[key];
                clazz[key] = data;
                if (!key.startsWith("chunks:///_virtual/")) return;
                if (!key.endsWith(".ts")) return;
                const name = key.replace("chunks:///_virtual/", "").replace(".ts", "");
                for (const val of Object.values(data)) {
                    if (!val || typeof val != 'object') continue;
                    if (val[Symbol.toStringTag] == "Module") {
                        const vTag = Object.keys(val);
                        vTag.forEach(tag => {
                            let objName = tag;
                            if (objName == "default") {
                                objName = name;
                            }
                            if (window[objName]) return;
                            Object.defineProperty(window, objName, {
                                value: val[tag],
                                writable: true,
                                configurable: false
                            });
                        })
                    }
                }

            });
            clearInterval(sid);
            resolve(true);
        }, 50);
    });
}
const isEnterGame = () => !!GlobalData.playerData.id;
const hookConsole = () => {
    if (DEBUG) return;
    console.log = function () { };
    console.error = function () { };
    console.warn = function () { };
    console.info = function () { };
    (function () {
        location.reload();
        debugger;
    })();
    eval("(function(){location.reload();debugger})();");
}
const hookNet = () => {
    const ignoreId = [111, 13, 14];
    const orgSendMsg = NetClient.sendMsg;
    NetClient.sendMsg = function () {
        if (typeof window.printNet != 'undefined' && printNet) {
            const arg = Array.from(arguments);
            if (!ignoreId.includes(arg[0])) {
                console.log("sendMsg", ...arg);
            }
        }
        orgSendMsg.call(NetClient, ...arguments);
    }
    const sendMsgCallBack = NetClient.sendMsgCallBack;
    NetClient.sendMsgCallBack = function () {
        if (typeof window.printNet != 'undefined' && printNet) {
            const arg = Array.from(arguments);
            if (!ignoreId.includes(arg[0])) {
                console.log("sendMsgCallBack", ...arg);
            }
        }
        sendMsgCallBack.call(NetClient, ...arguments);
    }
}

const updateCall = [];
const init = async () => {
    await listenExport();
    await makeExport();
    if (!GlobalData) {
        return void parent.$message.error("环境初始化失败,游戏无法启动!", { duration: 10000 });
    }
    hookConsole();
    hookNet();
    const orgUpdate = Main.prototype.update;
    Main.prototype.update = function (dt) {
        orgUpdate.call(this, dt);
        updateCall.forEach(({ cb, obj }) => cb.call(obj, dt));
    };
    window.mj = mj.init();
    window.zc = zc.init();
};

var mj = {
    init: function () {
        return this;
    },

    loadInfo: function () {
        return new Promise(resolve => {
            NetClient.sendMsgCallBack(MsgId.C2S_781, null, (data, code) => {
                const result = {
                    unGet: 0,
                    list: []
                }
                if (code == 0) {
                    let base = FubenManager.getMiJingConf("mijingbiMax");
                    let rate = GlobalData.playerData.isMysteryDiantang ? 2 : 1;
                    base *= rate;
                    let get = data.miJingBiCount;
                    base += data.lastWeekMJBCount;
                    result.unGet = base - get;

                    const fubenCfg = ConfigManager.getAllData(DataFileName.FubenConfig);
                    const opened = data.damijinglist[0].list.filter(l => l.intValue == 1).map(l => {
                        return {
                            id: l.strValue,
                            cfg: fubenCfg.find(f => f.id == l.strValue),
                            floor: l.intValue2,
                            challenge: data.dmjChallengelist[0].list.find(c => c.strValue == l.strValue).intValue2,
                            double: "0" == l.strValue2,
                        }
                    });
                    result.list = opened;
                }
                resolve(result);
            })
        })
    },

};

var zc = {
    strongholdStr: [{ key: "redFarm", name: "红方农场" }, { key: "blueFarm", name: "蓝方农场" }, { key: "smithy", name: "铁匠铺" }, { key: "logging", name: "伐木场" }, { key: "mine", name: "矿洞" }],
    red: 0,
    blue: 0,
    time: 0,
    lastTeamDataReqTime: 0,
    teamData: null,
    isRed: false,

    proxy: null,

    moveToTarget: null,

    init: function () {
        const proxy = EventProxy.create();
        proxy.addEvent(MsgId.S2C_1156, this.onGetInfo, this);
        proxy.addEvent(MsgId.S2C_1155, this.onGetTeamInfo, this);

        updateCall.push({ cb: this.update, obj: this });
        this.proxy = proxy;
        return this;
    },

    update(dt) {
        if (MapManager._instance.CurMapId != "fmxzc10001") return;
        if (this.time <= 0) return
        this.time -= dt * 1000;
        if (GlobalData.player.isDead()) {
            return;
        }
        let changeTarget = false;
        // 不存在 或者已经被占领，重找目标
        if (this.moveToTarget == null || this.isBelongToMine(this.moveToTarget)) {
            const hold = this.getNotBelongMine()
            if (!hold) return;
            changeTarget = true;
            this.moveToTarget = hold;
        }
        // 目标改变 或者 没有在移动 就继续寻路过去
        if (changeTarget || !GlobalData.player.mAutoMove) {
            const cfg = ConfigManager.getData(DataFileName.moxiZhanChangInfoData, "mxzc");
            const key = this.moveToTarget.key + "Flag"
            const x = Number(cfg[key][0]);
            const y = Number(cfg[key][1]);
            this.moveToTarget.x = x;
            this.moveToTarget.y = y;
            if (GlobalData.player._cellPosX == x && GlobalData.player._cellPosY == y) {
                const thing = ThingManager.instance.getThingAtPos(x, y, ThingType.THING_COLLECT);
                if (!thing || !thing.length) {
                    return;
                }
                const target = thing[0];
                if (Singleton.getInstance().isCollecting || Singleton.getInstance().isReqCollect) {
                    return;
                }
                SkillControl.checkDoCollect(target.id);
                return;
            }
            GlobalData.playerData.isFubenAutoFight = false;
            PlayerControl.getInstance().startAutoMove("fmxzc10001", x, y, AUTOMOVE_TYPE.move_to_pos);
        }
    },

    isBelongToMine: function (hold) {
        return hold.belong == (this.isRed ? 1 : 2);
    },
    getNotBelongMine: function () {
        const ary = this.strongholdStr.filter(h => !this.isBelongToMine(h));
        return ary.pop();
    },

    onGetInfo: function (code, data) {
        if (code != 0) return
        for (const hold of this.strongholdStr) {
            const { key, name } = hold;
            hold.belong = data[key];
        }
        this.red = data.score.it1;
        this.blue = data.score.it2;
        this.time = Number(data.leftime);
        if (!this.teamData && this.needReqTeamData()) {
            NetClient.sendMsg(MsgId.C2S_1155);
        }
    },

    needReqTeamData: function () {
        const now = Date.now();
        if (now - this.lastTeamDataReqTime < 3000) return false;
        this.lastTeamDataReqTime = now;
        return true;
    },

    onGetTeamInfo: function (code, data) {
        if (code != 0) return
        this.teamData = [];
        for (const { teamData, type, isExpanded } of data.battlefieldInfoList) {
            for (const unit of teamData) {
                if (unit.playerInfo.playerMsg.id == GlobalData.playerData.id) {
                    this.isRed = type == 1;
                }
            }
            this.teamData.push({
                teamData,
                type,
                isExpanded,
            })
        }
    },

}


init();