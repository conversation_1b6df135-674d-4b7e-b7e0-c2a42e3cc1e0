import com.google.javascript.jscomp.AbstractCommandLineRunner;
import com.google.javascript.jscomp.CommandLineRunner;
import com.sun.javafx.PlatformUtil;
import org.apache.commons.codec.digest.DigestUtils;
import tool.RandomUtil;
import tool.SevenZip;
import tool.SystemUtil;

import java.io.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class PackTool {


    static String path = "";
    final static StringBuffer buffer = new StringBuffer();
    final static StringBuffer archiveBuffer = new StringBuffer();

    static HashMap<String, String> md5Map = new HashMap<>();

    public static void main(String[] args) throws Exception {
        String rootDir = System.getProperty("user.dir");
        Path root = Paths.get(rootDir).getParent();
        path = root.toFile().getAbsolutePath() + File.separator;
        // 压缩js代码文件成一行
        PackTool.compress("logic.js");
        PackTool.compress("mlogic.js");
        PackTool.compress("applogic.js");

        // 编译前端项目文件
        PackTool.build("dashboard");
        PackTool.build("game");
        PackTool.build("mini_program");
        PackTool.build("app_ios");

        // 加密文件
        PackTool.doEncrypt("min.logic.js", "build/_min.logic.js", true);
        PackTool.doEncrypt("ware.json", "build/_ware.json", false);
        PackTool.doEncrypt("min.mlogic.js", "build/_min.mlogic.js", true);
        PackTool.doEncrypt("mware.json", "build/_mware.json", false);
        PackTool.doEncrypt("min.applogic.js", "build/_min.applogic.js", true);
        PackTool.doEncrypt("appware.json", "build/_appware.json", false);
        // 加密html
        PackTool.doEncrypt("dashboard/index.html", "build/v5/dashboard/index.bin", false);
        PackTool.doEncrypt("game/index.html", "build/v5/game/index.bin", false);
        PackTool.doEncrypt("mini_program/index.html", "build/v5/mini_program/index.bin", false);
        PackTool.doEncrypt("app_ios/index.html", "build/v5/app_ios/index.bin", false);

        Path output = Paths.get("").toAbsolutePath().resolve("src").resolve("test").resolve("build");
        File file = output.resolve("md5.txt").toFile();
        // 写入md5文件
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream fos = new FileOutputStream(file);
        StringBuilder outBuffer = new StringBuilder();
        for (Map.Entry<String, String> stringStringEntry : md5Map.entrySet()) {
            outBuffer.append(stringStringEntry.getKey()).append(":").append(stringStringEntry.getValue()).append("\n");
        }
        fos.write(outBuffer.toString().getBytes());
        fos.close();


//        PackTool.build("dashboard");
//        PackTool.build("game");

        // MD5 写入 后续需要加密写入
//        Path output = Paths.get("").toAbsolutePath().resolve("src").resolve("test");
//        File md5 = output.resolve("md5.bin").toFile();
//        boolean ignore = md5.createNewFile();
//        ignore = md5.setWritable(true);
//        new FileOutputStream(md5).write((buffer.toString()).getBytes());
//        SevenZip sevenZip = SevenZip.create();
//        Path source = Paths.get(path + name + "/" + name + ".7z");
//        Path target = Paths.get(path + "/" + name + ".7z");

        System.out.println("pack end...");
    }


    private static void build(String name) throws Exception {
        System.out.println("编译前端项目:" + name);
        //进入到 path目录下执行 npm run build命令
        PackTool.execCmd("npm run build", new File(path + File.separator + name));
        Path source = Paths.get(path + name + File.separator + "dist" + File.separator + "index.html");
        if (!source.toFile().exists()) {
            System.out.println("编译前端项目后找不到index.html !");
            System.exit(-1);
        }
        Path base = Paths.get("").toAbsolutePath().resolve("src").resolve("test");
        Path target = base.resolve(name).resolve("index.html");
        File file = target.toFile();
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        Files.move(source, target, StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.ATOMIC_MOVE);

        file.deleteOnExit();


//        Path source = Paths.get(path + name + "/dist/");
//        // 生成md5文件
//        File file = source.toFile();
//        PackTool.traverseDirectoryGenerateMd5(file, buffer);
//        // 把dist压缩成7z
//        SevenZip sevenZip = SevenZip.create();
//        Path output = Paths.get("").toAbsolutePath().resolve("src").resolve("test");
//        Path target = output.resolve(name + ".7z");
//        sevenZip.compress(target, source);
//        archiveBuffer.append(name).append(".7z").append(":").append(DigestUtils.md5Hex(new FileInputStream(target.toFile()))).append("\n");
    }

    public static void traverseDirectoryGenerateMd5(File file, StringBuffer buffer) throws Exception {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            assert files != null;
            for (File f : files) {
                traverseDirectoryGenerateMd5(f, buffer);
            }
        }
        if (file.isFile()) {
            FileInputStream fileInputStream = new FileInputStream(file);
            String md5 = DigestUtils.md5Hex(fileInputStream);
            buffer.append(file.getPath().replace(path, "")).append(":").append(md5).append("\n");
        }
    }

    public static void execCmd(String cmd, File dir) throws Exception {
        Process process = null;
        if (PlatformUtil.isWindows()) {
            cmd = "cmd /c " + cmd;
        }
        try {
            // 执行命令, 返回一个子进程对象（命令在子进程中执行）
            process = Runtime.getRuntime().exec(cmd, null, dir);
            StreamGobbler errorGobbler = new StreamGobbler(process.getErrorStream(), "ERROR");
            // kick off stderr
            errorGobbler.start();
            StreamGobbler outGobbler = new StreamGobbler(process.getInputStream(), "STDOUT");
            // kick off stdout
            outGobbler.start();
            // 方法阻塞, 等待命令执行完成（成功会返回0）
            int code = process.waitFor();
            System.out.println("execCmd 执行结果:" + code);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 销毁子进程
            if (process != null) {
                process.destroy();
            }
        }
    }

    public static void compress(String name) throws Exception {
        System.out.println("压缩文件:" + name);
        Path base = Paths.get("").toAbsolutePath().resolve("src").resolve("test");
        File file = base.resolve(name).toFile();
        Path out = base.resolve("_" + name);

        String[] args = new String[]{"--js", file.getAbsolutePath(), "--js_output_file", base.resolve("min." + name).toFile().getAbsolutePath(), "--warning_level", "QUIET"};
        Class<?>[] argTypes = {String[].class};

        Constructor<CommandLineRunner> con = CommandLineRunner.class.getDeclaredConstructor(argTypes);
        con.setAccessible(true);
        CommandLineRunner constructor = con.newInstance((Object) args);
        Field config = AbstractCommandLineRunner.class.getDeclaredField("config");
        config.setAccessible(true);
        Object o = config.get(constructor);
        o.getClass().getMethod("setCharset", new Class[]{String.class}).invoke(o, "UTF-8");
        constructor.setExitCodeReceiver((exitCode) -> {
            System.out.println("压缩结果:" + exitCode);
            return null;
        });
        constructor.run();
    }

    public static void doEncrypt(String name, String outName, Boolean deleteSource) throws Exception {
        System.out.println("开始制作加密文件:" + name);
        Path base = Paths.get("").toAbsolutePath().resolve("src").resolve("test");
        Path in = base.toAbsolutePath();
        if (name.contains(File.separator)) {
            String[] split = name.split(File.separator);
            for (int i = 0; i < split.length - 1; i++) {
                String t = split[i];
                if (t.equals("")) continue;
                in = in.resolve(t);
            }
            name = split[split.length - 1];
        }
        File file = in.resolve(name).toFile();
        Path out = base.resolve(outName);
        if (!out.toFile().getParentFile().exists()) {
            out.toFile().getParentFile().mkdirs();
        }
        // 首位写一个随机魔术 1-127
        int magicNumber = RandomUtil.random(1, 127);
        // 获取文件大小
        long fileSize = Files.size(file.toPath());
        // 3. 读取文件A的内容，与魔术数字M进行异或操作，然后写入文件B
        byte[] fileContentA = Files.readAllBytes(file.toPath());
        byte[] xorContent = xorWithMagicNumber(fileContentA, magicNumber);

        // 4. 将魔术数字M、文件A的大小和异或后的内容写入文件B
        BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(out));
        try {
            bos.write(ByteBuffer.allocate(4).putInt(magicNumber).array()); // 写入魔术数字M
            bos.write(ByteBuffer.allocate(8).putLong(fileSize).array()); // 写入文件A的大小
            bos.write(xorContent); // 写入异或后的内容
        } catch (Exception e) {
            e.printStackTrace();
        }
        bos.close();
        if (deleteSource) {
            file.deleteOnExit();
        }

        Path root = Paths.get("").toAbsolutePath().resolve("src").resolve("test").resolve("build");
        String rootPath = root.toFile().getAbsolutePath();
        File outFile = out.toFile();
        String nameKey = outFile.getAbsolutePath().replace(rootPath + File.separator, "");
        nameKey = nameKey.replace(File.separator, "/");
        // 为文件生成md5
        String md5 = DigestUtils.md5Hex(new FileInputStream(outFile));
        md5Map.put(nameKey, md5);
    }

    private static byte[] xorWithMagicNumber(byte[] content, int magicNumber) {
        byte[] xorContent = new byte[content.length];
        for (int i = 0; i < content.length; i++) {
            xorContent[i] = (byte) (content[i] ^ magicNumber); // 对每一位进行异或操作
        }
        return xorContent;
    }

}

class StreamGobbler extends Thread {
    InputStream is;
    String type;
    OutputStream os;

    StreamGobbler(InputStream is, String type) {
        this(is, type, null);
    }

    StreamGobbler(InputStream is, String type, OutputStream redirect) {
        this.is = is;
        this.type = type;
        this.os = redirect;
    }

    public void run() {
        InputStreamReader isr = null;
        BufferedReader br = null;
        PrintWriter pw = null;
        try {
            if (os != null)
                pw = new PrintWriter(os);

            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            String line = null;
            while ((line = br.readLine()) != null) {
                if (pw != null)
                    pw.println(line);
                // System.out.println(type + ">" + line);
            }

            if (pw != null)
                pw.flush();
        } catch (IOException ioe) {
            ioe.printStackTrace();
        } finally {
            try {
                if (pw != null)
                    pw.close();
                if (isr != null)
                    isr.close();
                if (br != null)
                    br.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
}