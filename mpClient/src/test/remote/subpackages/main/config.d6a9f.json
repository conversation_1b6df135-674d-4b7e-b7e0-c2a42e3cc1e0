{"importBase": "import", "nativeBase": "native", "name": "main", "deps": ["resources", "start-scene"], "uuids": ["02MpXvfspIhJYIZOEJKvvp", "6fjdAag3xF2pO2a3vhivu0", "95anegtjRJkoG8q2KOiYEV", "1epxEQnvZNWJZRplACIdXZ", "50l5UEQFFHCYtSvnEVn/mW", "5cg8fpN69H7LH24b4gsGei", "63snwFAZZEm4q5MM6k/cur", "ea2PqyuYFI/otYXdxLbcdg", "01a52da7b", "0c31cba98", "13iPuq9JBMd63Sy5gdsHXO", "1cKxmGiHhCGJmX4JbbRuk+", "1epxEQnvZNWJZRplACIdXZ@6c48a", "1epxEQnvZNWJZRplACIdXZ@f9941", "30A25BxZNL4IXP7nGnXiyp@f9941", "3bJHtDRAVJX57xWjaUM1yv", "42YlG8C+VFQIQhi/uTnF/K", "5cg8fpN69H7LH24b4gsGei@6c48a", "5cg8fpN69H7LH24b4gsGei@f9941", "63snwFAZZEm4q5MM6k/cur@6c48a", "63snwFAZZEm4q5MM6k/cur@f9941", "75Y95m7phJp52lzyY6XTsO", "94FGSZedBCE46Kdw4aaHkw@f9941", "99zsborI9EQpKxhoIDEO4Q@f9941", "d5xxX9FgdEXrimuuZccNUd", "ea2PqyuYFI/otYXdxLbcdg@6c48a", "ea2PqyuYFI/otYXdxLbcdg@f9941", "faQO1SSTFOvo4Bd/0qdjpc", "faQO1SSTFOvo4Bd/0qdjpc@24c53", "faQO1SSTFOvo4Bd/0qdjpc@25adb", "faQO1SSTFOvo4Bd/0qdjpc@3eb79", "faQO1SSTFOvo4Bd/0qdjpc@40510", "faQO1SSTFOvo4Bd/0qdjpc@5a989", "faQO1SSTFOvo4Bd/0qdjpc@5ce1b", "faQO1SSTFOvo4Bd/0qdjpc@67f4a", "faQO1SSTFOvo4Bd/0qdjpc@6b2bf", "faQO1SSTFOvo4Bd/0qdjpc@760ca", "faQO1SSTFOvo4Bd/0qdjpc@8026d", "faQO1SSTFOvo4Bd/0qdjpc@8c199", "faQO1SSTFOvo4Bd/0qdjpc@8da69", "faQO1SSTFOvo4Bd/0qdjpc@993b1", "faQO1SSTFOvo4Bd/0qdjpc@a77c2", "faQO1SSTFOvo4Bd/0qdjpc@acae1", "faQO1SSTFOvo4Bd/0qdjpc@c0180", "faQO1SSTFOvo4Bd/0qdjpc@c5574", "faQO1SSTFOvo4Bd/0qdjpc@cb6a6", "faQO1SSTFOvo4Bd/0qdjpc@d95ee", "faQO1SSTFOvo4Bd/0qdjpc@dd058", "faQO1SSTFOvo4Bd/0qdjpc@de01f", "faQO1SSTFOvo4Bd/0qdjpc@e6287", "faQO1SSTFOvo4Bd/0qdjpc@e65fc", "faQO1SSTFOvo4Bd/0qdjpc@f93a6", "faQO1SSTFOvo4Bd/0qdjpc@fc8d6"], "paths": {"0": ["db:/assets/scene/relogin", 0, 1], "1": ["db:/assets/scene/zoneServer", 0, 1], "2": ["db:/assets/scene/main", 0, 1]}, "scenes": {"db://assets/scene/main.scene": 2, "db://assets/scene/relogin.scene": 0, "db://assets/scene/zoneServer.scene": 1}, "packs": {"01a52da7b": [12, 17, 19, 25], "0c31cba98": [13, 18, 20, 1, 26]}, "versions": {"import": [8, "a866a", 0, "6303f", 9, "304d7", 3, "a2533", 5, "a2533", 6, "a2533", 2, "a3a92", 7, "a2533"], "native": [3, "d2b14", 5, "6823e", 6, "62ac4", 7, "7b322"]}, "redirect": [10, "0", 11, "0", 14, "1", 15, "0", 16, "0", 4, "1", 21, "0", 22, "0", 23, "1", 24, "0", 27, "0", 28, "0", 29, "0", 30, "0", 31, "0", 32, "0", 33, "0", 34, "0", 35, "0", 36, "0", 37, "0", 38, "0", 39, "0", 40, "0", 41, "0", 42, "0", 43, "0", 44, "0", 45, "0", 46, "0", 47, "0", 48, "0", 49, "0", 50, "0", 51, "0", 52, "0"], "debug": false, "extensionMap": {".ccon": [4]}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.SceneAsset"]}