
	var global = (function () { return this })();	if (!global && typeof GameGlobal !== 'undefined') global = GameGlobal;	var pluginInfoMap = {};	;	global.requirePlugin = global.requirePlugin || function(path) {		    var position = path.indexOf('/');    		var alias = '';    		var pagePath = '';    		if (position !== -1) {    		    alias = path.substr(0, position);    		    pagePath = path.substr(position + 1, path.length);    		}    		else {    		    alias = path;    		}    		if (pluginInfoMap.hasOwnProperty(alias)) {    		    var realPath = '';    		    if (pagePath.length === 0) {    		        realPath =  '__plugin__/' + pluginInfoMap[alias].appid;    		        return require(realPath);    		    } else {    		        realPath = '__plugin__/' + pluginInfoMap[alias].appid + '/' + pagePath;    		        return require(realPath);    		    }    		}    		else {    		    console.error('not found alias: ', alias);    		    throw new Error('Plugin ' + alias + ' is not defined.')    		}	};	define("cocos-js/chunks/game.js", function(require, module, exports){ 			
"use strict";require("../../@babel/runtime/helpers/Arrayincludes");var t=require("../../@babel/runtime/helpers/createForOfIteratorHelper"),e=require("../../@babel/runtime/helpers/defineProperty"),n=require("../../@babel/runtime/helpers/typeof");console.log("[CC Subpackage] wasm chunks loaded"),System.register("external:emscripten/spine/spine.wasm.js",[],(function(r){return{execute:function(){var o;r("default",(o="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(){var r,a,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=void 0!==i?i:{};s.ready=new Promise((function(t,e){r=t,a=e}));var u=Object.assign({},s),c=!0,l="";function f(t){return s.locateFile?s.locateFile(t,l):l+t}"undefined"!=typeof document&&document.currentScript&&(l=document.currentScript.src),o&&(l=o),l=0!==l.indexOf("blob:")?l.substr(0,l.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var p,d,h=s.print||console.log.bind(console),v=s.printErr||console.error.bind(console);Object.assign(s,u),u=null,s.arguments&&s.arguments,s.thisProgram&&s.thisProgram,s.quit&&s.quit,s.wasmBinary&&(p=s.wasmBinary),s.noExitRuntime,"object"!=("undefined"==typeof WebAssembly?"undefined":n(WebAssembly))&&z("no native wasm support detected");var y,m,g,b,C,w,$,T,P,A=!1;function S(t,e){t||z(e)}function W(){var t=d.buffer;s.HEAP8=y=new Int8Array(t),s.HEAP16=g=new Int16Array(t),s.HEAP32=C=new Int32Array(t),s.HEAPU8=m=new Uint8Array(t),s.HEAPU16=b=new Uint16Array(t),s.HEAPU32=w=new Uint32Array(t),s.HEAPF32=$=new Float32Array(t),s.HEAPF64=T=new Float64Array(t)}var k=[],E=[],O=[];function _(){if(s.preRun)for("function"==typeof s.preRun&&(s.preRun=[s.preRun]);s.preRun.length;)R(s.preRun.shift());Z(k)}function j(){Z(E)}function F(){if(s.postRun)for("function"==typeof s.postRun&&(s.postRun=[s.postRun]);s.postRun.length;)x(s.postRun.shift());Z(O)}function R(t){k.unshift(t)}function D(t){E.unshift(t)}function x(t){O.unshift(t)}var I=0,U=null;function V(t){I++,s.monitorRunDependencies&&s.monitorRunDependencies(I)}function H(t){if(I--,s.monitorRunDependencies&&s.monitorRunDependencies(I),0==I&&U){var e=U;U=null,e()}}function z(t){s.onAbort&&s.onAbort(t),v(t="Aborted("+t+")"),A=!0,t+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(t);throw a(e),e}var B,L="data:application/octet-stream;base64,";function q(t){return t.startsWith(L)}function M(t){try{if(t==B&&p)return new Uint8Array(p);throw"both async and sync fetching of the wasm failed"}catch(t){z(t)}}function G(t){return!p&&c&&"function"==typeof fetch?fetch(t,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+t+"'";return e.arrayBuffer()})).catch((function(){return M(t)})):Promise.resolve().then((function(){return M(t)}))}function N(t,e,n){return G(t).then((function(t){return WebAssembly.instantiate(t,e)})).then((function(t){return t})).then(n,(function(t){v("failed to asynchronously prepare wasm: "+t),z(t)}))}function J(t,e,n,r){return t||"function"!=typeof WebAssembly.instantiateStreaming||q(e)||"function"!=typeof fetch?N(e,n,r):fetch(e,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,n).then(r,(function(t){return v("wasm streaming compile failed: "+t),v("falling back to ArrayBuffer instantiation"),N(e,n,r)}))}))}function K(){var t={a:Tn};function e(t,e){var n=t.exports;return s.asm=n,d=s.asm.I,W(),P=s.asm.K,D(s.asm.J),H(),n}if(V(),s.instantiateWasm)try{return s.instantiateWasm(t,e)}catch(t){v("Module.instantiateWasm callback failed with error: "+t),a(t)}return J(p,B,t,(function(t){e(t.instance)})).catch(a),{}}function Z(t){for(;t.length>0;)t.shift()(s)}q(B="spine.wasm")||(B=f(B));var Q="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function X(t,e,n){for(var r=e+n,o=e;t[o]&&!(o>=r);)++o;if(o-e>16&&t.buffer&&Q)return Q.decode(t.subarray(e,o));for(var a="";e<o;){var i=t[e++];if(128&i){var s=63&t[e++];if(192!=(224&i)){var u=63&t[e++];if((i=224==(240&i)?(15&i)<<12|s<<6|u:(7&i)<<18|s<<12|u<<6|63&t[e++])<65536)a+=String.fromCharCode(i);else{var c=i-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&i)<<6|s)}else a+=String.fromCharCode(i)}return a}function Y(t,e){return t?X(m,t,e):""}function tt(t,e,n){return 0}function et(t,e,n){return 0}function nt(t,e,n,r){}function rt(t,e,n,r,o){}function ot(t){switch(t){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: ".concat(t))}}function at(){for(var t=new Array(256),e=0;e<256;++e)t[e]=String.fromCharCode(e);it=t}var it=void 0;function st(t){for(var e="",n=t;m[n];)e+=it[m[n++]];return e}var ut={},ct={},lt={},ft=48,pt=57;function dt(t){if(void 0===t)return"_unknown";var e=(t=t.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return e>=ft&&e<=pt?"_".concat(t):t}function ht(t,n){return t=dt(t),e({},t,(function(){return n.apply(this,arguments)}))[t]}function vt(t,e){var n=ht(e,(function(t){this.name=e,this.message=t;var n=new Error(t).stack;void 0!==n&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return n.prototype=Object.create(t.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},n}var yt=void 0;function mt(t){throw new yt(t)}var gt=void 0;function bt(t){throw new gt(t)}function Ct(t,e,n){function r(e){var r=n(e);r.length!==t.length&&bt("Mismatched type converter count");for(var o=0;o<t.length;++o)wt(t[o],r[o])}t.forEach((function(t){lt[t]=e}));var o=new Array(e.length),a=[],i=0;e.forEach((function(t,e){ct.hasOwnProperty(t)?o[e]=ct[t]:(a.push(t),ut.hasOwnProperty(t)||(ut[t]=[]),ut[t].push((function(){o[e]=ct[t],++i===a.length&&r(o)})))})),0===a.length&&r(o)}function wt(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in e))throw new TypeError("registerType registeredInstance requires argPackAdvance");var r=e.name;if(t||mt('type "'.concat(r,'" must have a positive integer typeid pointer')),ct.hasOwnProperty(t)){if(n.ignoreDuplicateRegistrations)return;mt("Cannot register type '".concat(r,"' twice"))}if(ct[t]=e,delete lt[t],ut.hasOwnProperty(t)){var o=ut[t];delete ut[t],o.forEach((function(t){return t()}))}}function $t(t,e,n,r,o){var a=ot(n);wt(t,{name:e=st(e),fromWireType:function(t){return!!t},toWireType:function(t,e){return e?r:o},argPackAdvance:8,readValueFromPointer:function(t){var r;if(1===n)r=y;else if(2===n)r=g;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+e);r=C}return this.fromWireType(r[t>>a])},destructorFunction:null})}function Tt(t){if(!(this instanceof Zt))return!1;if(!(t instanceof Zt))return!1;for(var e=this.$$.ptrType.registeredClass,n=this.$$.ptr,r=t.$$.ptrType.registeredClass,o=t.$$.ptr;e.baseClass;)n=e.upcast(n),e=e.baseClass;for(;r.baseClass;)o=r.upcast(o),r=r.baseClass;return e===r&&n===o}function Pt(t){return{count:t.count,deleteScheduled:t.deleteScheduled,preservePointerOnDelete:t.preservePointerOnDelete,ptr:t.ptr,ptrType:t.ptrType,smartPtr:t.smartPtr,smartPtrType:t.smartPtrType}}function At(t){mt(t.$$.ptrType.registeredClass.name+" instance already deleted")}var St=!1;function Wt(t){}function kt(t){t.smartPtr?t.smartPtrType.rawDestructor(t.smartPtr):t.ptrType.registeredClass.rawDestructor(t.ptr)}function Et(t){t.count.value-=1,0===t.count.value&&kt(t)}function Ot(t,e,n){if(e===n)return t;if(void 0===n.baseClass)return null;var r=Ot(t,e,n.baseClass);return null===r?null:n.downcast(r)}var _t={};function jt(){return Object.keys(Vt).length}function Ft(){var t=[];for(var e in Vt)Vt.hasOwnProperty(e)&&t.push(Vt[e]);return t}var Rt=[];function Dt(){for(;Rt.length;){var t=Rt.pop();t.$$.deleteScheduled=!1,t.delete()}}var xt=void 0;function It(t){xt=t,Rt.length&&xt&&xt(Dt)}function Ut(){s.getInheritedInstanceCount=jt,s.getLiveInheritedInstances=Ft,s.flushPendingDeletes=Dt,s.setDelayFunction=It}var Vt={};function Ht(t,e){for(void 0===e&&mt("ptr should not be undefined");t.baseClass;)e=t.upcast(e),t=t.baseClass;return e}function zt(t,e){return e=Ht(t,e),Vt[e]}function Bt(t,e){return e.ptrType&&e.ptr||bt("makeClassHandle requires ptr and ptrType"),!!e.smartPtrType!=!!e.smartPtr&&bt("Both smartPtrType and smartPtr must be specified"),e.count={value:1},qt(Object.create(t,{$$:{value:e}}))}function Lt(t){var e=this.getPointee(t);if(!e)return this.destructor(t),null;var n=zt(this.registeredClass,e);if(void 0!==n){if(0===n.$$.count.value)return n.$$.ptr=e,n.$$.smartPtr=t,n.clone();var r=n.clone();return this.destructor(t),r}function o(){return this.isSmartPointer?Bt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:e,smartPtrType:this,smartPtr:t}):Bt(this.registeredClass.instancePrototype,{ptrType:this,ptr:t})}var a,i=this.registeredClass.getActualType(e),s=_t[i];if(!s)return o.call(this);a=this.isConst?s.constPointerType:s.pointerType;var u=Ot(e,this.registeredClass,a.registeredClass);return null===u?o.call(this):this.isSmartPointer?Bt(a.registeredClass.instancePrototype,{ptrType:a,ptr:u,smartPtrType:this,smartPtr:t}):Bt(a.registeredClass.instancePrototype,{ptrType:a,ptr:u})}function qt(t){return"undefined"==typeof FinalizationRegistry?(qt=function(t){return t},t):(St=new FinalizationRegistry((function(t){Et(t.$$)})),Wt=function(t){return St.unregister(t)},(qt=function(t){var e=t.$$;if(e.smartPtr){var n={$$:e};St.register(t,n,t)}return t})(t))}function Mt(){if(this.$$.ptr||At(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var t=qt(Object.create(Object.getPrototypeOf(this),{$$:{value:Pt(this.$$)}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function Gt(){this.$$.ptr||At(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&mt("Object already scheduled for deletion"),Wt(this),Et(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Nt(){return!this.$$.ptr}function Jt(){return this.$$.ptr||At(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&mt("Object already scheduled for deletion"),Rt.push(this),1===Rt.length&&xt&&xt(Dt),this.$$.deleteScheduled=!0,this}function Kt(){Zt.prototype.isAliasOf=Tt,Zt.prototype.clone=Mt,Zt.prototype.delete=Gt,Zt.prototype.isDeleted=Nt,Zt.prototype.deleteLater=Jt}function Zt(){}function Qt(t,e,n){if(void 0===t[e].overloadTable){var r=t[e];t[e]=function(){return t[e].overloadTable.hasOwnProperty(arguments.length)||mt("Function '".concat(n,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(t[e].overloadTable,")!")),t[e].overloadTable[arguments.length].apply(this,arguments)},t[e].overloadTable=[],t[e].overloadTable[r.argCount]=r}}function Xt(t,e,n){s.hasOwnProperty(t)?((void 0===n||void 0!==s[t].overloadTable&&void 0!==s[t].overloadTable[n])&&mt("Cannot register public name '".concat(t,"' twice")),Qt(s,t,t),s.hasOwnProperty(n)&&mt("Cannot register multiple overloads of a function with the same number of arguments (".concat(n,")!")),s[t].overloadTable[n]=e):(s[t]=e,void 0!==n&&(s[t].numArguments=n))}function Yt(t,e,n,r,o,a,i,s){this.name=t,this.constructor=e,this.instancePrototype=n,this.rawDestructor=r,this.baseClass=o,this.getActualType=a,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}function te(t,e,n){for(;e!==n;)e.upcast||mt("Expected null or instance of ".concat(n.name,", got an instance of ").concat(e.name)),t=e.upcast(t),e=e.baseClass;return t}function ee(t,e){if(null===e)return this.isReference&&mt("null is not a valid ".concat(this.name)),0;e.$$||mt('Cannot pass "'.concat(ze(e),'" as a ').concat(this.name)),e.$$.ptr||mt("Cannot pass deleted object as a pointer of type ".concat(this.name));var n=e.$$.ptrType.registeredClass;return te(e.$$.ptr,n,this.registeredClass)}function ne(t,e){var n;if(null===e)return this.isReference&&mt("null is not a valid ".concat(this.name)),this.isSmartPointer?(n=this.rawConstructor(),null!==t&&t.push(this.rawDestructor,n),n):0;e.$$||mt('Cannot pass "'.concat(ze(e),'" as a ').concat(this.name)),e.$$.ptr||mt("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&e.$$.ptrType.isConst&&mt("Cannot convert argument of type ".concat(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name," to parameter type ").concat(this.name));var r=e.$$.ptrType.registeredClass;if(n=te(e.$$.ptr,r,this.registeredClass),this.isSmartPointer)switch(void 0===e.$$.smartPtr&&mt("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:e.$$.smartPtrType===this?n=e.$$.smartPtr:mt("Cannot convert argument of type ".concat(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:n=e.$$.smartPtr;break;case 2:if(e.$$.smartPtrType===this)n=e.$$.smartPtr;else{var o=e.clone();n=this.rawShare(n,De.toHandle((function(){o.delete()}))),null!==t&&t.push(this.rawDestructor,n)}break;default:mt("Unsupporting sharing policy")}return n}function re(t,e){if(null===e)return this.isReference&&mt("null is not a valid ".concat(this.name)),0;e.$$||mt('Cannot pass "'.concat(ze(e),'" as a ').concat(this.name)),e.$$.ptr||mt("Cannot pass deleted object as a pointer of type ".concat(this.name)),e.$$.ptrType.isConst&&mt("Cannot convert argument of type ".concat(e.$$.ptrType.name," to parameter type ").concat(this.name));var n=e.$$.ptrType.registeredClass;return te(e.$$.ptr,n,this.registeredClass)}function oe(t){return this.fromWireType(C[t>>2])}function ae(t){return this.rawGetPointee&&(t=this.rawGetPointee(t)),t}function ie(t){this.rawDestructor&&this.rawDestructor(t)}function se(t){null!==t&&t.delete()}function ue(){ce.prototype.getPointee=ae,ce.prototype.destructor=ie,ce.prototype.argPackAdvance=8,ce.prototype.readValueFromPointer=oe,ce.prototype.deleteObject=se,ce.prototype.fromWireType=Lt}function ce(t,e,n,r,o,a,i,s,u,c,l){this.name=t,this.registeredClass=e,this.isReference=n,this.isConst=r,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=l,o||void 0!==e.baseClass?this.toWireType=ne:r?(this.toWireType=ee,this.destructorFunction=null):(this.toWireType=re,this.destructorFunction=null)}function le(t,e,n){s.hasOwnProperty(t)||bt("Replacing nonexistant public symbol"),void 0!==s[t].overloadTable&&void 0!==n?s[t].overloadTable[n]=e:(s[t]=e,s[t].argCount=n)}function fe(t,e,n){var r=s["dynCall_"+t];return n&&n.length?r.apply(null,[e].concat(n)):r.call(null,e)}var pe=[];function de(t){var e=pe[t];return e||(t>=pe.length&&(pe.length=t+1),pe[t]=e=P.get(t)),e}function he(t,e,n){return t.includes("j")?fe(t,e,n):de(e).apply(null,n)}function ve(t,e){var n=[];return function(){return n.length=0,Object.assign(n,arguments),he(t,e,n)}}function ye(t,e){var n=(t=st(t)).includes("j")?ve(t,e):de(e);return"function"!=typeof n&&mt("unknown function pointer with signature ".concat(t,": ").concat(e)),n}var me=void 0;function ge(t){var e=Wn(t),n=st(e);return Sn(e),n}function be(t,e){var n=[],r={};throw e.forEach((function t(e){r[e]||ct[e]||(lt[e]?lt[e].forEach(t):(n.push(e),r[e]=!0))})),new me("".concat(t,": ")+n.map(ge).join([", "]))}function Ce(t,e,n,r,o,a,i,s,u,c,l,f,p){l=st(l),a=ye(o,a),s&&(s=ye(i,s)),c&&(c=ye(u,c)),p=ye(f,p);var d=dt(l);Xt(d,(function(){be("Cannot construct ".concat(l," due to unbound types"),[r])})),Ct([t,e,n],r?[r]:[],(function(e){var n,o;e=e[0],o=r?(n=e.registeredClass).instancePrototype:Zt.prototype;var i=ht(d,(function(){if(Object.getPrototypeOf(this)!==u)throw new yt("Use 'new' to construct "+l);if(void 0===f.constructor_body)throw new yt(l+" has no accessible constructor");var t=f.constructor_body[arguments.length];if(void 0===t)throw new yt("Tried to invoke ctor of ".concat(l," with invalid number of parameters (").concat(arguments.length,") - expected (").concat(Object.keys(f.constructor_body).toString(),") parameters instead!"));return t.apply(this,arguments)})),u=Object.create(o,{constructor:{value:i}});i.prototype=u;var f=new Yt(l,i,u,p,n,a,s,c);f.baseClass&&(void 0===f.baseClass.__derivedClasses&&(f.baseClass.__derivedClasses=[]),f.baseClass.__derivedClasses.push(f));var h=new ce(l,f,!0,!1,!1),v=new ce(l+"*",f,!1,!1,!1),y=new ce(l+" const*",f,!1,!0,!1);return _t[t]={pointerType:v,constPointerType:y},le(d,i),[h,v,y]}))}function we(t){for(;t.length;){var e=t.pop();t.pop()(e)}}function $e(t,e,n,r,o,a){var i=e.length;i<2&&mt("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==e[1]&&null!==n,u=!1,c=1;c<e.length;++c)if(null!==e[c]&&void 0===e[c].destructorFunction){u=!0;break}var l="void"!==e[0].name,f=i-2,p=new Array(f),d=[],h=[];return function(){var n;arguments.length!==f&&mt("function ".concat(t," called with ").concat(arguments.length," arguments, expected ").concat(f," args!")),h.length=0,d.length=s?2:1,d[0]=o,s&&(n=e[1].toWireType(h,this),d[1]=n);for(var a=0;a<f;++a)p[a]=e[a+2].toWireType(h,arguments[a]),d.push(p[a]);var i=r.apply(null,d);function c(t){if(u)we(h);else for(var r=s?1:2;r<e.length;r++){var o=1===r?n:p[r-2];null!==e[r].destructorFunction&&e[r].destructorFunction(o)}if(l)return e[0].fromWireType(t)}return c(i)}}function Te(t,e){for(var n=[],r=0;r<t;r++)n.push(w[e+4*r>>2]);return n}function Pe(e,n,r,o,a,i,s,u){var c=Te(r,o);n=st(n),i=ye(a,i),Ct([],[e],(function(e){e=e[0];var o="".concat(e.name,".").concat(n);function a(){be("Cannot call ".concat(o," due to unbound types"),c)}n.startsWith("@@")&&(n=Symbol[n.substring(2)]);var u=e.registeredClass.constructor;return void 0===u[n]?(a.argCount=r-1,u[n]=a):(Qt(u,n,o),u[n].overloadTable[r-1]=a),Ct([],c,(function(a){var c=[a[0],null].concat(a.slice(1)),l=$e(o,c,null,i,s);if(void 0===u[n].overloadTable?(l.argCount=r-1,u[n]=l):u[n].overloadTable[r-1]=l,e.registeredClass.__derivedClasses){var f,p=t(e.registeredClass.__derivedClasses);try{for(p.s();!(f=p.n()).done;){var d=f.value;d.constructor.hasOwnProperty(n)||(d.constructor[n]=l)}}catch(t){p.e(t)}finally{p.f()}}return[]})),[]}))}function Ae(t,e,n){return t instanceof Object||mt("".concat(n,' with invalid "this": ').concat(t)),t instanceof e.registeredClass.constructor||mt("".concat(n,' incompatible with "this" of type ').concat(t.constructor.name)),t.$$.ptr||mt("cannot call emscripten binding method ".concat(n," on deleted object")),te(t.$$.ptr,t.$$.ptrType.registeredClass,e.registeredClass)}function Se(t,e,n,r,o,a,i,s){e=st(e),a=ye(o,a),Ct([],[t],(function(t){t=t[0];var o="".concat(t.name,".").concat(e),u={get:function(){be("Cannot access ".concat(o," due to unbound types"),[n])},enumerable:!0,configurable:!0};return u.set=s?function(){be("Cannot access ".concat(o," due to unbound types"),[n])}:function(t){mt("".concat(o," is a read-only property"))},Object.defineProperty(t.registeredClass.constructor,e,u),Ct([],[n],(function(n){n=n[0];var o={get:function(){return n.fromWireType(a(r))},enumerable:!0};return s&&(s=ye(i,s),o.set=function(t){var e=[];s(r,n.toWireType(e,t)),we(e)}),Object.defineProperty(t.registeredClass.constructor,e,o),[]})),[]}))}function We(t,e,n,r,o,a){S(e>0);var i=Te(e,n);o=ye(r,o),Ct([],[t],(function(t){t=t[0];var n="constructor ".concat(t.name);if(void 0===t.registeredClass.constructor_body&&(t.registeredClass.constructor_body=[]),void 0!==t.registeredClass.constructor_body[e-1])throw new yt("Cannot register multiple constructors with identical number of parameters (".concat(e-1,") for class '").concat(t.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return t.registeredClass.constructor_body[e-1]=function(){be("Cannot construct ".concat(t.name," due to unbound types"),i)},Ct([],i,(function(r){return r.splice(1,0,null),t.registeredClass.constructor_body[e-1]=$e(n,r,null,o,a),[]})),[]}))}function ke(t,e,n,r,o,a,i,s,u){var c=Te(n,r);e=st(e),a=ye(o,a),Ct([],[t],(function(t){t=t[0];var r="".concat(t.name,".").concat(e);function o(){be("Cannot call ".concat(r," due to unbound types"),c)}e.startsWith("@@")&&(e=Symbol[e.substring(2)]),s&&t.registeredClass.pureVirtualFunctions.push(e);var u=t.registeredClass.instancePrototype,l=u[e];return void 0===l||void 0===l.overloadTable&&l.className!==t.name&&l.argCount===n-2?(o.argCount=n-2,o.className=t.name,u[e]=o):(Qt(u,e,r),u[e].overloadTable[n-2]=o),Ct([],c,(function(o){var s=$e(r,o,t,a,i);return void 0===u[e].overloadTable?(s.argCount=n-2,u[e]=s):u[e].overloadTable[n-2]=s,[]})),[]}))}function Ee(t,e,n,r,o,a,i,s,u,c){e=st(e),o=ye(r,o),Ct([],[t],(function(t){t=t[0];var r="".concat(t.name,".").concat(e),l={get:function(){be("Cannot access ".concat(r," due to unbound types"),[n,i])},enumerable:!0,configurable:!0};return l.set=u?function(){be("Cannot access ".concat(r," due to unbound types"),[n,i])}:function(t){mt(r+" is a read-only property")},Object.defineProperty(t.registeredClass.instancePrototype,e,l),Ct([],u?[n,i]:[n],(function(n){var i=n[0],l={get:function(){var e=Ae(this,t,r+" getter");return i.fromWireType(o(a,e))},enumerable:!0};if(u){u=ye(s,u);var f=n[1];l.set=function(e){var n=Ae(this,t,r+" setter"),o=[];u(c,n,f.toWireType(o,e)),we(o)}}return Object.defineProperty(t.registeredClass.instancePrototype,e,l),[]})),[]}))}function Oe(){this.allocated=[void 0],this.freelist=[],this.get=function(t){return this.allocated[t]},this.has=function(t){return void 0!==this.allocated[t]},this.allocate=function(t){var e=this.freelist.pop()||this.allocated.length;return this.allocated[e]=t,e},this.free=function(t){this.allocated[t]=void 0,this.freelist.push(t)}}var _e=new Oe;function je(t){t>=_e.reserved&&0==--_e.get(t).refcount&&_e.free(t)}function Fe(){for(var t=0,e=_e.reserved;e<_e.allocated.length;++e)void 0!==_e.allocated[e]&&++t;return t}function Re(){_e.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),_e.reserved=_e.allocated.length,s.count_emval_handles=Fe}var De={toValue:function(t){return t||mt("Cannot use deleted val. handle = "+t),_e.get(t).value},toHandle:function(t){switch(t){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return _e.allocate({refcount:1,value:t})}}};function xe(t,e){wt(t,{name:e=st(e),fromWireType:function(t){var e=De.toValue(t);return je(t),e},toWireType:function(t,e){return De.toHandle(e)},argPackAdvance:8,readValueFromPointer:oe,destructorFunction:null})}function Ie(t,e,n){switch(e){case 0:return function(t){var e=n?y:m;return this.fromWireType(e[t])};case 1:return function(t){var e=n?g:b;return this.fromWireType(e[t>>1])};case 2:return function(t){var e=n?C:w;return this.fromWireType(e[t>>2])};default:throw new TypeError("Unknown integer type: "+t)}}function Ue(t,e,n,r){var o=ot(n);function a(){}e=st(e),a.values={},wt(t,{name:e,constructor:a,fromWireType:function(t){return this.constructor.values[t]},toWireType:function(t,e){return e.value},argPackAdvance:8,readValueFromPointer:Ie(e,o,r),destructorFunction:null}),Xt(e,a)}function Ve(t,e){var n=ct[t];return void 0===n&&mt(e+" has unknown type "+ge(t)),n}function He(t,e,n){var r=Ve(t,"enum");e=st(e);var o=r.constructor,a=Object.create(r.constructor.prototype,{value:{value:n},constructor:{value:ht("".concat(r.name,"_").concat(e),(function(){}))}});o.values[n]=a,o[e]=a}function ze(t){if(null===t)return"null";var e=n(t);return"object"===e||"array"===e||"function"===e?t.toString():""+t}function Be(t,e){switch(e){case 2:return function(t){return this.fromWireType($[t>>2])};case 3:return function(t){return this.fromWireType(T[t>>3])};default:throw new TypeError("Unknown float type: "+t)}}function Le(t,e,n){var r=ot(n);wt(t,{name:e=st(e),fromWireType:function(t){return t},toWireType:function(t,e){return e},argPackAdvance:8,readValueFromPointer:Be(e,r),destructorFunction:null})}function qe(t,e,n){switch(e){case 0:return n?function(t){return y[t]}:function(t){return m[t]};case 1:return n?function(t){return g[t>>1]}:function(t){return b[t>>1]};case 2:return n?function(t){return C[t>>2]}:function(t){return w[t>>2]};default:throw new TypeError("Unknown integer type: "+t)}}function Me(t,e,n,r,o){e=st(e);var a=ot(n),i=function(t){return t};if(0===r){var s=32-8*n;i=function(t){return t<<s>>>s}}var u=e.includes("unsigned");wt(t,{name:e,fromWireType:i,toWireType:u?function(t,e){return this.name,e>>>0}:function(t,e){return this.name,e},argPackAdvance:8,readValueFromPointer:qe(e,a,0!==r),destructorFunction:null})}function Ge(t,e,n){var r=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];function o(t){var e=w,n=e[t>>=2],o=e[t+1];return new r(e.buffer,o,n)}wt(t,{name:n=st(n),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})}function Ne(t,e,n,r){if(!(r>0))return 0;for(var o=n,a=n+r-1,i=0;i<t.length;++i){var s=t.charCodeAt(i);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&t.charCodeAt(++i)),s<=127){if(n>=a)break;e[n++]=s}else if(s<=2047){if(n+1>=a)break;e[n++]=192|s>>6,e[n++]=128|63&s}else if(s<=65535){if(n+2>=a)break;e[n++]=224|s>>12,e[n++]=128|s>>6&63,e[n++]=128|63&s}else{if(n+3>=a)break;e[n++]=240|s>>18,e[n++]=128|s>>12&63,e[n++]=128|s>>6&63,e[n++]=128|63&s}}return e[n]=0,n-o}function Je(t,e,n){return Ne(t,m,e,n)}function Ke(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);r<=127?e++:r<=2047?e+=2:r>=55296&&r<=57343?(e+=4,++n):e+=3}return e}function Ze(t,e){var n="std::string"===(e=st(e));wt(t,{name:e,fromWireType:function(t){var e,r=w[t>>2],o=t+4;if(n)for(var a=o,i=0;i<=r;++i){var s=o+i;if(i==r||0==m[s]){var u=Y(a,s-a);void 0===e?e=u:(e+=String.fromCharCode(0),e+=u),a=s+1}}else{var c=new Array(r);for(i=0;i<r;++i)c[i]=String.fromCharCode(m[o+i]);e=c.join("")}return Sn(t),e},toWireType:function(t,e){var r;e instanceof ArrayBuffer&&(e=new Uint8Array(e));var o="string"==typeof e;o||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||mt("Cannot pass non-string to std::string"),r=n&&o?Ke(e):e.length;var a=An(4+r+1),i=a+4;if(w[a>>2]=r,n&&o)Je(e,i,r+1);else if(o)for(var s=0;s<r;++s){var u=e.charCodeAt(s);u>255&&(Sn(i),mt("String has UTF-16 code units that do not fit in 8 bits")),m[i+s]=u}else for(s=0;s<r;++s)m[i+s]=e[s];return null!==t&&t.push(Sn,a),a},argPackAdvance:8,readValueFromPointer:oe,destructorFunction:function(t){Sn(t)}})}var Qe="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Xe(t,e){for(var n=t,r=n>>1,o=r+e/2;!(r>=o)&&b[r];)++r;if((n=r<<1)-t>32&&Qe)return Qe.decode(m.subarray(t,n));for(var a="",i=0;!(i>=e/2);++i){var s=g[t+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a}function Ye(t,e,n){if(void 0===n&&(n=2147483647),n<2)return 0;for(var r=e,o=(n-=2)<2*t.length?n/2:t.length,a=0;a<o;++a){var i=t.charCodeAt(a);g[e>>1]=i,e+=2}return g[e>>1]=0,e-r}function tn(t){return 2*t.length}function en(t,e){for(var n=0,r="";!(n>=e/4);){var o=C[t+4*n>>2];if(0==o)break;if(++n,o>=65536){var a=o-65536;r+=String.fromCharCode(55296|a>>10,56320|1023&a)}else r+=String.fromCharCode(o)}return r}function nn(t,e,n){if(void 0===n&&(n=2147483647),n<4)return 0;for(var r=e,o=r+n-4,a=0;a<t.length;++a){var i=t.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&t.charCodeAt(++a)),C[e>>2]=i,(e+=4)+4>o)break}return C[e>>2]=0,e-r}function rn(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);r>=55296&&r<=57343&&++n,e+=4}return e}function on(t,e,n){var r,o,a,i,s;n=st(n),2===e?(r=Xe,o=Ye,i=tn,a=function(){return b},s=1):4===e&&(r=en,o=nn,i=rn,a=function(){return w},s=2),wt(t,{name:n,fromWireType:function(t){for(var n,o=w[t>>2],i=a(),u=t+4,c=0;c<=o;++c){var l=t+4+c*e;if(c==o||0==i[l>>s]){var f=r(u,l-u);void 0===n?n=f:(n+=String.fromCharCode(0),n+=f),u=l+e}}return Sn(t),n},toWireType:function(t,r){"string"!=typeof r&&mt("Cannot pass non-string to C++ string type ".concat(n));var a=i(r),u=An(4+a+e);return w[u>>2]=a>>s,o(r,u+4,a+e),null!==t&&t.push(Sn,u),u},argPackAdvance:8,readValueFromPointer:oe,destructorFunction:function(t){Sn(t)}})}function an(t,e){wt(t,{isVoid:!0,name:e=st(e),argPackAdvance:0,fromWireType:function(){},toWireType:function(t,e){}})}function sn(t,e,n){t=De.toValue(t),e=Ve(e,"emval::as");var r=[],o=De.toHandle(r);return w[n>>2]=o,e.toWireType(r,t)}function un(t){t>4&&(_e.get(t).refcount+=1)}function cn(t){we(De.toValue(t)),je(t)}function ln(t,e){var n=(t=Ve(t,"_emval_take_value")).readValueFromPointer(e);return De.toHandle(n)}function fn(){z("")}function pn(t,e,n){m.copyWithin(t,e,e+n)}function dn(t){var e=t-d.buffer.byteLength+65535>>>16;try{return d.grow(e),W(),1}catch(t){}}function hn(t){var e=m.length;if((t>>>=0)>2147483648)return!1;for(var n,r,o=1;o<=4;o*=2){var a=e*(1+.2/o);if(a=Math.min(a,t+100663296),dn(Math.min(2147483648,(n=Math.max(t,a))+((r=65536)-n%r)%r)))return!0}return!1}function vn(t){return 52}function yn(t,e,n,r){return 52}function mn(t,e,n,r,o){return 70}var gn=[null,[],[]];function bn(t,e){var n=gn[t];0===e||10===e?((1===t?h:v)(X(n,0)),n.length=0):n.push(e)}function Cn(t,e,n,r){for(var o=0,a=0;a<n;a++){var i=w[e>>2],s=w[e+4>>2];e+=8;for(var u=0;u<s;u++)bn(t,m[i+u]);o+=s}return w[r>>2]=o,0}function wn(){var t=s.SpineWasmUtil,e=t.getCurrentListenerID(),n=t.getCurrentTrackEntry(),r=t.getCurrentEvent(),o=t.getCurrentEventType();globalThis.TrackEntryListeners.emitListener(e,n,r,o.value)}function $n(){var t=s.SpineWasmUtil,e=t.getCurrentListenerID(),n=t.getCurrentEventType(),r=t.getCurrentTrackEntry(),o=t.getCurrentEvent();globalThis.TrackEntryListeners.emitTrackEntryListener(e,r,o,n.value)}at(),yt=s.BindingError=vt(Error,"BindingError"),gt=s.InternalError=vt(Error,"InternalError"),Kt(),Ut(),ue(),me=s.UnboundTypeError=vt(Error,"UnboundTypeError"),Re();var Tn={o:tt,x:et,y:nt,t:rt,C:$t,b:Ce,f:Pe,j:Se,c:We,a:ke,e:Ee,A:xe,k:Ue,d:He,p:Le,l:Me,i:Ge,q:Ze,m:on,D:an,F:sn,r:je,G:un,E:cn,h:ln,g:fn,z:pn,u:hn,n:vn,w:yn,s:mn,v:Cn,H:wn,B:$n};K();var Pn,An=function(){return(An=s.asm.L).apply(null,arguments)},Sn=function(){return(Sn=s.asm.M).apply(null,arguments)},Wn=function(){return(Wn=s.asm.N).apply(null,arguments)};function kn(){function t(){Pn||(Pn=!0,s.calledRun=!0,A||(j(),r(s),s.onRuntimeInitialized&&s.onRuntimeInitialized(),F()))}I>0||(_(),I>0||(s.setStatus?(s.setStatus("Running..."),setTimeout((function(){setTimeout((function(){s.setStatus("")}),1),t()}),1)):t()))}if(s.__embind_initialize_bindings=function(){return(s.__embind_initialize_bindings=s.asm.O).apply(null,arguments)},s.dynCall_jiji=function(){return(s.dynCall_jiji=s.asm.P).apply(null,arguments)},U=function t(){Pn||kn(),Pn||(U=t)},s.preInit)for("function"==typeof s.preInit&&(s.preInit=[s.preInit]);s.preInit.length>0;)s.preInit.pop()();return kn(),i.ready}))}}})),System.register("external:emscripten/spine/spine.wasm",[],(function(t,e){return{setters:[],execute:function(){t("default","assets/spine.wasm")}}})),System.register("external:emscripten/spine/spine.asm.js",[],(function(t,e){return t("default",(function(){})),{setters:[],execute:function(){}}})),System.register("external:emscripten/spine/spine.js.mem",[],(function(t,e){return{setters:[],execute:function(){t("default","")}}})),System.register("external:emscripten/meshopt/meshopt_decoder.wasm.js",[],(function(t,e){return t("default",(function(){})),{setters:[],execute:function(){}}})),System.register("external:emscripten/meshopt/meshopt_decoder.wasm.wasm",[],(function(t,e){return{setters:[],execute:function(){t("default","")}}})),System.register("external:emscripten/meshopt/meshopt_decoder.asm.js",[],(function(t,e){return t("default",(function(){})),{setters:[],execute:function(){}}})); 
 			}); 	require("cocos-js/chunks/game.js");
 	