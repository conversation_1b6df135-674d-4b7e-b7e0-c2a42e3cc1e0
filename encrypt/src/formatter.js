/**
 * 文件格式化模块
 * 用于格式化JS和JSON文件，提高可读性
 */

const fs = require('fs');
const path = require('path');
const { js_beautify } = require('js-beautify');

/**
 * 格式化选项配置
 */
const JS_FORMAT_OPTIONS = {
    indent_size: 2,
    indent_char: ' ',
    max_preserve_newlines: 2,
    preserve_newlines: true,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'collapse',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: 120,
    indent_inner_html: false,
    comma_first: false,
    e4x: false,
    indent_empty_lines: false
};

const JSON_FORMAT_OPTIONS = {
    indent_size: 2,
    indent_char: ' ',
    end_with_newline: true
};

/**
 * 格式化JavaScript代码
 * @param {string} code - 原始JS代码
 * @returns {string} - 格式化后的代码
 */
function formatJavaScript(code) {
    try {
        return js_beautify(code, JS_FORMAT_OPTIONS);
    } catch (error) {
        console.warn(`JS格式化失败: ${error.message}`);
        return code; // 返回原始代码
    }
}

/**
 * 格式化JSON代码
 * @param {string} jsonString - 原始JSON字符串
 * @returns {string} - 格式化后的JSON
 */
function formatJSON(jsonString) {
    try {
        // 先解析确保是有效的JSON
        const parsed = JSON.parse(jsonString);
        // 重新格式化
        return JSON.stringify(parsed, null, JSON_FORMAT_OPTIONS.indent_size);
    } catch (error) {
        console.warn(`JSON格式化失败: ${error.message}`);
        return jsonString; // 返回原始字符串
    }
}

/**
 * 判断文件是否需要格式化
 * @param {string} filePath - 文件路径
 * @returns {boolean} - 是否需要格式化
 */
function shouldFormat(filePath) {
    // const ext = path.extname(filePath).toLowerCase();
    // return ext === '.js' || ext === '.json';
    return false
}

/**
 * 格式化单个文件
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} - 是否成功格式化
 */
async function formatFile(filePath) {
    try {
        if (!shouldFormat(filePath)) {
            return false;
        }

        const content = await fs.promises.readFile(filePath, 'utf8');
        const ext = path.extname(filePath).toLowerCase();
        
        let formattedContent;
        if (ext === '.js') {
            formattedContent = formatJavaScript(content);
        } else if (ext === '.json') {
            formattedContent = formatJSON(content);
        } else {
            return false;
        }

        // 只有在内容发生变化时才写入文件
        if (formattedContent !== content) {
            await fs.promises.writeFile(filePath, formattedContent, 'utf8');
            return true;
        }
        
        return false;
    } catch (error) {
        console.warn(`格式化文件失败 ${filePath}: ${error.message}`);
        return false;
    }
}

/**
 * 递归格式化目录中的所有JS和JSON文件
 * @param {string} dirPath - 目录路径
 * @returns {Promise<{formatted: number, total: number}>} - 格式化统计
 */
async function formatDirectory(dirPath) {
    let formatted = 0;
    let total = 0;

    async function scanDirectory(currentPath) {
        const items = await fs.promises.readdir(currentPath);
        
        for (const item of items) {
            const fullPath = path.join(currentPath, item);
            const stat = await fs.promises.stat(fullPath);
            
            if (stat.isFile()) {
                if (shouldFormat(fullPath)) {
                    total++;
                    const wasFormatted = await formatFile(fullPath);
                    if (wasFormatted) {
                        formatted++;
                    }
                }
            } else if (stat.isDirectory()) {
                await scanDirectory(fullPath);
            }
        }
    }
    
    await scanDirectory(dirPath);
    return { formatted, total };
}

/**
 * 格式化指定路径的文件或目录
 * @param {string} targetPath - 目标路径
 * @returns {Promise<{formatted: number, total: number}>} - 格式化统计
 */
async function format(targetPath) {
    try {
        const stat = await fs.promises.stat(targetPath);
        
        if (stat.isFile()) {
            const wasFormatted = await formatFile(targetPath);
            return {
                formatted: wasFormatted ? 1 : 0,
                total: shouldFormat(targetPath) ? 1 : 0
            };
        } else if (stat.isDirectory()) {
            return await formatDirectory(targetPath);
        } else {
            return { formatted: 0, total: 0 };
        }
    } catch (error) {
        console.error(`格式化失败 ${targetPath}: ${error.message}`);
        return { formatted: 0, total: 0 };
    }
}

module.exports = {
    format,
    formatFile,
    formatDirectory,
    formatJavaScript,
    formatJSON,
    shouldFormat
};
