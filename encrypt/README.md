# 微信小程序包处理工具

这是一个用于处理微信小程序包（.wxapkg文件）的Node.js工具，集成了解密和解压缩功能。

## 功能特性

- **自动解密**: 检测并解密加密的小程序包文件
- **完整解压缩**: 提取小程序包中的所有文件
- **批量处理**: 支持处理单个文件或整个目录
- **智能识别**: 自动识别主包、分包和小游戏包
- **简洁输出**: 最小化控制台输出，只显示重要信息

## 项目结构

```
├── index.js          # 主程序入口
├── src/
│   ├── decrypt.js     # 解密模块
│   ├── unpack.js      # 解压缩模块
│   └── utils.js       # 工具函数
├── out/               # 输出目录
│   ├── .tmp/          # 临时文件目录
│   └── [解压结果]/     # 各个小程序的解压结果
└── package.json       # 项目配置
```

## 安装依赖

```bash
npm install
```

## 使用方法

### 处理单个文件
```bash
node index.js <file_path> [appid]
```

### 处理目录中的所有.wxapkg文件
```bash
node index.js <directory_path> [appid]
```

### 示例
```bash
# 处理加密文件（需要提供AppID）
node index.js __APP__.wxapkg wx0c747e2a0ec57737

# 处理未加密文件（不需要AppID）
node index.js unencrypted.wxapkg

# 处理指定目录下的所有小程序包
node index.js ./wxapkg-files/ wx0c747e2a0ec57737
```

## 输出说明

- **解密后的临时文件**: 保存在 `out/.tmp/` 目录下
- **最终解压结果**: 保存在 `out/[AppID]/` 目录下（如果提供了AppID），否则保存在 `out/[文件名]/` 目录下
- **自动清理**: 处理完成后自动删除临时文件

## 支持的文件类型

- **加密的小程序包**: 以"V1MMWX"开头的加密文件
- **未加密的小程序包**: 标准的wxapkg格式文件
- **小程序主包**: 包含app-service.js的主包
- **小游戏包**: 包含game.js的游戏包
- **分包**: 小程序的子包文件

## 技术实现

### 解密算法
- **检测**: 通过文件头"V1MMWX"识别加密文件
- **密钥派生**: 使用PBKDF2算法，基于小程序AppID和固定盐值"saltiest"
- **解密方式**: AES-256-CBC + XOR异或加密组合
- **AppID获取**: 对于加密文件，需要知道对应的微信小程序AppID才能正确解密

### 解压缩流程
- **文件头解析**: 解析wxapkg文件的头部信息
- **文件列表**: 提取包含的所有文件信息
- **数据提取**: 按偏移量和大小提取各个文件
- **后处理**: 根据包类型进行配置文件转换

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在或格式错误
- 解密失败的详细原因
- 解压缩过程中的异常
- 自动清理临时文件

## 测试文件

项目包含多个测试脚本：
- `manual-test.js`: 手动测试完整流程
- `simple-test.js`: 简化的功能验证
- `run-test.js`: 自动化测试脚本

运行测试：
```bash
node manual-test.js
```

## 注意事项

1. **重要**: 对于加密的小程序包，必须知道对应的微信小程序AppID才能成功解密
2. 确保有足够的磁盘空间存储解压结果
3. 分包文件需要与主包一起处理才能完整还原
4. 输出的文件可能需要进一步的格式化处理

## AppID说明

- **加密文件**: 必须提供正确的AppID才能成功解密
- **未加密文件**: 不需要提供AppID，可以直接解压缩
- **获取AppID**: 可以通过微信开发者工具或小程序管理后台获取
- **AppID格式**: 通常以`wx`开头，如`wx1234567890abcdef`

## 基于项目

本工具基于以下开源项目的核心逻辑：
- **UnpackMiniApp**: C#版本的解密工具
- **wxappUnpacker**: Node.js版本的解压缩工具

## 许可证

MIT License
