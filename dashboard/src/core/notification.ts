// // 消息通知提示音
// const notificationAudio = new Audio('https://img-fe.tengzhihh.com/audio/c58fb135c2546f.mp3');
// // 消息弹窗数组
// const notificationInstanceArr: Array<{ [propName: string]: number | Notification }> = [];
// export const handleNotification = () => {
// // 检查是否支持
//     if (!('Notification' in window)) {
//         console.log('浏览器不支持消息通知');
//         return;
//     }
// // 创建配置项
//     const options = {
//         body: '您有新的未读消息', // 展示内容
//         silent: false, // 是否静音
//         lang: 'ZH',
//         sticky: true, // 是否粘性展示，不轻易被清理
//         // renotify: true, // 弹窗内容更新，是否重新通知，需与tag搭配使用
//         requireInteraction: true, // 是否保持，不自动关闭
//     };
// // 检查权限是否已获取 已获取为granted
//     if (Notification.permission !== 'granted') {
//         // 未允许权限，则申请权限
//         Notification.requestPermission(function (status) {
//             if (status == 'granted') {
//                 // 创建提醒
//                 const notification = new Notification('新消息提醒', options);
//                 // 设置点击事件
//                 notification.onclick = function () {
//                     handleMessageFocus(data);
//                 };
//                 // 存储通知实例，保留最新三个
//                 if (notificationInstanceArr.length == 3) {
//                     const instance = notificationInstanceArr.shift()?.instance;
//                     instance?.close();
//                 }
//                 notificationInstanceArr.push({
//                     id: target.id,
//                     instance: notification,
//                 });
//                 // 播放通知音频
//                 notificationAudio?.play();
//             }
//         });
//     } else {
//         // 已有权限，重复操作即可
//         const notification = new Notification('新消息提醒', options);
//         notification.onclick = function () {
//             handleMessageFocus(data);
//         };
//         if (notificationInstanceArr.length == 3) {
//             const instance = notificationInstanceArr.shift()?.instance;
//             instance?.close();
//         }
//         notificationInstanceArr.push({
//             id: target.id,
//             instance: notification,
//         });
//         notificationAudio?.play();
//     }
// }
//
// function handleMessageFocus(target) {
//     // 可以在这里 根据新消息target，处理展示具体会话逻辑
//     // ...
//     // 展示原有页面
//     window.focus();
//     // 删除点击的会话通知
//     for (let i = 0; i < notificationInstanceArr.length; i++) {
//         if (notificationInstanceArr[i].id == target.id) {
//             notificationInstanceArr[i].instance.close();
//             notificationInstanceArr.splice(i, 1);
//             i--;
//         }
//     }
// }