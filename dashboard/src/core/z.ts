export type Record = {
    id: string;
    sort: number;
    name: string;
    state: STATE;
    channel: ChannelType;
    password: string;
}

export type Key = {
    sort: number;
    name: string;
    tip: string;
    modify: boolean;
    status: boolean[];
}

// 运行状态
export enum STATE {
    STARTING, // 启动中
    RUNNING, // 运行中
    STOPPING, // 停止中
    STOPPED, // 停止
}

export enum StorageKey {
    DASHBOARD_DATA = 'DASHBOARD_DATA',
}

export type Channel = {
    name: string;
    url: string;
    icon: string
}

export enum ChannelType {
    NONE,
    GUAN_FANG,
}

export const Channels: Channel[] = [
    {
        name: "未知",
        url: "",
        icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAkRJREFUWEftlj9oFEEUxr/vDhRR0trZaaFgo4Xxb2zE83YRBdOI3CyCaSyDEBtj4x/EyipBbudaRZC4e9E0Rgwa0FTB1kqwSKdCkMN9Zjd7mKy7N7vc4iFkytn3vu83b2beDjHgwQH7YwsgswJyTp1FhScBGe5vmzgHBEv0WnNpOqkAYjtTELnWn3EyWxbotU4kZ/8CEKvxFuDxcs1jNXKaL9yxjdqbAMRqnAH4Kof5BwBNBFiEdD6jsn0ECG6AOGbMDVBjW7/sxiUAnAlA7hhE2uhUr2Do2yp+7tiLTvUrZ92VMEfqasEMwbv03JsZAOo1gJHeAJxFFeP4JU8AHACwApH79FsPxXKuA/LIsIB5evp0HwBR6qfYvKsTiUpdXQIRgvUapQBsNhAZo9+alrp6DOLqvwa4TU9PiqXCrQu30DRKrcBzevpCAfMQrkyA4Ch+rC5j187wTOwxLT3+Xh4APU2x1REI3uc0L7sCBWz/hJZXAQhG167d/jXtyQIoPQBs9QyCi7nE4qsXd0AXhMqVB87Qc89nteI8naybG12/CMBy3hX4bU/Q0/fSAeqNQyA/5ltJ6AwNcjcgtdw5AQ6yrZdTAaLV2M44RB7kFiwSKBilr59uTEl/kKx3tSkA+4roZ8fKFwhv0dfNZEz2k6x2eQiVbcNgv08yzKNSWeJM83sa4P/xKhbbsSByOKPEkjovXKTvGl9XuSoglko3MRyQsFWbzpAxYP2eq/C+nzKJJb6/6faJXnm5AAoaFwrfAhh4BX4D9F7dIf3yE60AAAAASUVORK5CYII="
    },
    {
        name: "微信",
        url: "game://index.html",
        icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAkRJREFUWEftlj9oFEEUxr/vDhRR0trZaaFgo4Xxb2zE83YRBdOI3CyCaSyDEBtj4x/EyipBbudaRZC4e9E0Rgwa0FTB1kqwSKdCkMN9Zjd7mKy7N7vc4iFkytn3vu83b2beDjHgwQH7YwsgswJyTp1FhScBGe5vmzgHBEv0WnNpOqkAYjtTELnWn3EyWxbotU4kZ/8CEKvxFuDxcs1jNXKaL9yxjdqbAMRqnAH4Kof5BwBNBFiEdD6jsn0ECG6AOGbMDVBjW7/sxiUAnAlA7hhE2uhUr2Do2yp+7tiLTvUrZ92VMEfqasEMwbv03JsZAOo1gJHeAJxFFeP4JU8AHACwApH79FsPxXKuA/LIsIB5evp0HwBR6qfYvKsTiUpdXQIRgvUapQBsNhAZo9+alrp6DOLqvwa4TU9PiqXCrQu30DRKrcBzevpCAfMQrkyA4Ch+rC5j187wTOwxLT3+Xh4APU2x1REI3uc0L7sCBWz/hJZXAQhG167d/jXtyQIoPQBs9QyCi7nE4qsXd0AXhMqVB87Qc89nteI8naybG12/CMBy3hX4bU/Q0/fSAeqNQyA/5ltJ6AwNcjcgtdw5AQ6yrZdTAaLV2M44RB7kFiwSKBilr59uTEl/kKx3tSkA+4roZ8fKFwhv0dfNZEz2k6x2eQiVbcNgv08yzKNSWeJM83sa4P/xKhbbsSByOKPEkjovXKTvGl9XuSoglko3MRyQsFWbzpAxYP2eq/C+nzKJJb6/6faJXnm5AAoaFwrfAhh4BX4D9F7dIf3yE60AAAAASUVORK5CYII="
    },
]


export const ZoomMap: { [key: number]: number } = {
    1: 0.25,
    2: 0.3333333333333333,
    3: 0.5,
    4: 0.6666666666666666,
    5: 0.75,
    6: 0.8,
    7: 0.9,
    8: 1.0,
    9: 1.1,
    10: 1.25,
    11: 1.5,
    12: 1.75,
    13: 2.0,
    14: 2.5,
    15: 3.0,
    16: 4.0,
    17: 5.0,
}
