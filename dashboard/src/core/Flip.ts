type pos = {
    x: number; y: number
}

class FlipDom {
    public dom: HTMLElement;
    public transition: string;
    public firstPosition: pos;
    public isPlaying: boolean;
    public transitionEndHandler: () => void;


    constructor(dom: HTMLElement, duration = 0.3) {
        this.dom = dom;
        this.transition = `${duration}s`;
        this.firstPosition = {
            x: 0,
            y: 0,
        };
        this.isPlaying = false;
        this.transitionEndHandler = () => {
            this.isPlaying = false;
            this.recordFirst();
        };
    }

    public getDomPosition() {
        const rect = this.dom.getBoundingClientRect();
        return {
            x: rect.left,
            y: rect.top,
        };
    }

    public recordFirst(firstPosition?: pos) {
        if (!firstPosition) {
            firstPosition = this.getDomPosition();
        }
        this.firstPosition.x = firstPosition.x;
        this.firstPosition.y = firstPosition.y;
    }

    * play() {
        if (!this.isPlaying) {
            this.dom.style.transition = 'none';
            const lastPosition = this.getDomPosition();
            const dis = {
                x: lastPosition.x - this.firstPosition.x,
                y: lastPosition.y - this.firstPosition.y,
            };
            if (!dis.x && !dis.y) {
                return;
            }
            this.dom.style.transform = `translate(${-dis.x}px, ${-dis.y}px)`;
            yield 'moveToFirst';
            this.isPlaying = true;
        }

        this.dom.style.transition = this.transition;
        this.dom.style.transform = `none`;
        this.dom.removeEventListener('transitionend', this.transitionEndHandler);
        this.dom.addEventListener('transitionend', this.transitionEndHandler);
    }
}

export class Flip {
    public flipDoms: Set<FlipDom>;
    public duration: number;

    constructor(doms: HTMLElement[], duration = 0.3) {
        const fd = [...doms].map((it) => new FlipDom(it, duration));
        this.flipDoms = new Set(fd);
        this.duration = duration;
        this.flipDoms.forEach((it) => it.recordFirst());
    }

    public addDom(dom: HTMLElement, firstPosition: pos) {
        const flipDom = new FlipDom(dom, this.duration);
        this.flipDoms.add(flipDom);
        flipDom.recordFirst(firstPosition);
    }

    public isDone(){
        return [...this.flipDoms].every((it) => !it.isPlaying);
    }

    play() {
        let gs = [...this.flipDoms]
            .map((it) => {
                const generator = it.play();
                return {
                    generator,
                    iteratorResult: generator.next(),
                };
            })
            .filter((g) => !g.iteratorResult.done);

        while (gs.length > 0) {
            document.body.clientWidth;
            gs = gs
                .map((g) => {
                    g.iteratorResult = g.generator.next();
                    return g;
                })
                .filter((g) => !g.iteratorResult.done);
        }
    }
}


