import {ref} from 'vue'
import {defineStore} from 'pinia'
import ButtonGroup from "@/components/button-group/index.vue";
import {STATE, StorageKey} from "@/core/z";
import type {Record} from "@/core/z";
import Evt from "@/core/evt";
import {useOsTheme} from "vooks";

const osTheme = useOsTheme();

export const useUserStore = defineStore('user', () => {
    const model = ref({
        username: "",
        password: "",
        token: ""
    })

    const registerModel = ref({
        username: "",
        password: "",
        sign: ""
    })

    const userModel = ref({
        username: "",
        sign: "",
        machine: "",
        todayChangeMachineCnt: "",
        permission: new Map<string, boolean>()
    })

    const isLogin = ref(true)
    const isRegister = ref(false)
    const loginLoading = ref(false)
    const registerLoading = ref(false)

    const pinging = ref(false)
    const hideLogin = () => isLogin.value = false
    const hideRegister = () => isRegister.value = false

    const themeAuto = ref<boolean>(false)
    const themeType = ref<boolean>(false)

    const onThemeAuto = (v:boolean) => {
        themeAuto.value = v
        if (themeAuto.value) {
            localStorage.setItem("_is_theme_auto", "1")
        } else {
            localStorage.setItem("_is_theme_auto", "0")
        }
        Evt.emit("THEME_CHANGE", osTheme.value);
        onThemeType(osTheme.value != 'dark')
    }
    const onThemeType = (v:boolean) => {
        themeType.value = v
        const type = themeType.value ? 'light' : 'dark'
        localStorage.setItem("_theme_type", type)
        Evt.emit("THEME_CHANGE", type);
    }

    // 登录请求
    const loginRequest = async () => {
        const username = model.value.username
        const password = model.value.password
        if (!username || !password) return void window.$message.error("请输入用户名或密码!")
        loginLoading.value = true
        const code = await window.getMachine();
        const data = await window.postReq<{ token: string, machine: string }>("/login", {
            username,
            password,
            machine: code
        })
        loginLoading.value = false
        if (data) {
            const {token} = data
            model.value.token = token
            // 保存用户名和密码
            localStorage.setItem("_dashboard_username", username)
            localStorage.setItem("_dashboard_password", password)
            // 保存token
            localStorage.setItem("_dashboard_token", token)
            handleLogin(data)
        }
        checkForceUpdate()
    }

    const loginByToken = async () => {
        Evt.emit("EVT_DASHBOARD_LOADING", true, "自动登录...")
        const code = await window.getMachine();
        const data = await window.postReq<any>("/tokenLogin", {machine: code})
        handleLogin(data)
        Evt.emit("EVT_DASHBOARD_LOADING", false)
        checkForceUpdate()
    }

    // 获取是否有权限启动指定渠道
    const getCanLaunch = (channel: string | number) => {
        if (!userModel.value.permission) return
        typeof channel == "number" && (channel = channel.toString())
        return !!userModel.value.permission.get(channel)
    }

    const handleLogin = (data: any) => {
        if (data) {
            data.username && (userModel.value.username = data.username)
            data.sign && (userModel.value.sign = data.sign)
            data.machine && (userModel.value.machine = data.machine)
            data.todayChangeMachineCnt && (userModel.value.todayChangeMachineCnt = data.todayChangeMachineCnt)
            if (data.permission) {
                for (const key in data.permission) {
                    userModel.value.permission.set(key, data.permission[key])
                }
            }
            hideLogin()
            hideRegister()
            startPing()
            return
        }
        offlineRequest()
    }

    const registerRequest = async () => {
        const args = {
            username: registerModel.value.username,
            password: registerModel.value.password,
            sign: registerModel.value.sign,
        }
        registerLoading.value = true
        const data = await window.postReq<string>("/register", args)
        registerLoading.value = false
        if (data) {
            window.$message.success("请牢记注册码or账密")
            model.value.username = args.username
            model.value.password = args.password
            registerModel.value.password = ""
            return true
        }
        return false
    }

    const offlineRequest = async () => {
        await stopPing()
        try {
            const list = window.$dialogReactive
            list.value.forEach(l => l.destroy())
        } catch (e) {
            //console.error(e)
        }
        //
        model.value.token = ""
        //
        userModel.value.username = ""
        userModel.value.machine = ""
        userModel.value.sign = ""
        userModel.value.todayChangeMachineCnt = ""
        userModel.value.permission = new Map<string, boolean>()
        localStorage.removeItem("_dashboard_token")
        // 打开登录界面
        isLogin.value = true
        setTimeout(() => {
            if (userModel.value.username != "" && userModel.value.sign != "" && userModel.value.machine != "") {
                return
            }
            const item = localStorage.getItem(StorageKey.DASHBOARD_DATA);
            if (item) {
                const list = JSON.parse(item)
                list.forEach((l: Record) => window.DASHBOARD_REQ_STOP_GAME(l.id))
            }
        }, 6000)
    }
    const pingFailed = ref(false)
    const ping = async () => {
        const args = {
            username: userModel.value.username,
            machine: userModel.value.machine
        }
        const data = await window.postReq<any>("/ping", args)
        if (!data) {
            if (!pingFailed.value) {
                pingFailed.value = true
                return true
            }
        }
        if (!data || (data && !data.success)) {
            console.error("ping error")
            offlineRequest()
            const dlg = window.$dialog.error({
                title: "",
                closable: false,
                content: () => "登录信息验证失败，本机完全下线。",
                action: () => h(ButtonGroup, {
                    action: [
                        {label: "知道了", type: "success", callback: () => dlg.destroy()},
                    ]
                })
            })
            return false
        }
        pingFailed.value = false
        return true
    }

    // 每隔3分钟 ping 一次
    const startPing = async () => {
        pinging.value = true
        const logic = async () => {
            await window.wait(1000 * 3 * 60)
            if (pinging.value) {
                if (await ping()) {
                    return void await logic()
                } else {
                    pinging.value = false
                }
            }
        }
        await logic()
    }

    const stopPing = async () => {
        pinging.value = false
    }

    //@ts-ignore
    window["ping"] = ping

    const unbindRequest = async () => {
        window.$message.error("无需解绑")
    }


    const currentVersionRef = ref(0)
    const targetVersionRef = ref(0)
    // 后续检测更新操作
    const checkForceUpdate = () => {
        const ver = window.VERSION
        if (typeof ver == 'undefined') {
            notifyUpdate(410)
            currentVersionRef.value = 400
            targetVersionRef.value = 410
            return true
        }
        currentVersionRef.value = ver
        return false
    }

    const notifyUpdate = (ver: number) => {
        const dlg = window.$dialog.warning({
            title: "客户端更新提示",
            closeOnEsc: false,
            maskClosable: false,
            closable: false,
            content: () => "有新客户端版本，可能某些功能无法使用，必须更新，复制下载地址到任意浏览器中打开即可开始下载。",
            action: () => h(ButtonGroup, {
                action: [
                    {
                        label: "复制下载地址", type: "success", callback: (e:MouseEvent) => {
                            window.copy(`https://husong.vip/worldh5/release/${window.VER_STR}/${ver}.exe`, e.target)
                        }
                    }
                ]
            })
        })
    }

    return {
        isLogin,
        isRegister,
        model,
        registerModel,
        loginLoading,
        registerLoading,
        userModel,
        loginRequest,
        loginByToken,
        registerRequest,
        unbindRequest,
        offlineRequest,
        getCanLaunch,
        themeAuto,
        themeType,
        onThemeType,
        onThemeAuto,
        currentVersionRef,
        targetVersionRef,
        checkForceUpdate
    }
})
