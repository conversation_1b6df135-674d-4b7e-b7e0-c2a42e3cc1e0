<template>
  <n-config-provider :theme="theme as BuiltInGlobalTheme">
    <n-message-provider>
      <message-api></message-api>
    </n-message-provider>
    <n-dialog-provider>
      <dialog-api></dialog-api>
    </n-dialog-provider>
    <login v-show="userStore.isLogin" />
    <register v-show="userStore.isRegister" />
    <dashboard />
    <n-global-style />
  </n-config-provider>
</template>

<style>
* {
  user-select: none
}

body {}

/* 设置滚动条样式 */
::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: #79cbca;
}

::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: #e684ae;
  -webkit-transition: all .2s;
  transition: all .2s;
}

::-webkit-scrollbar-corner {
  background-color: #f2f3f8;
}

::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
</style>
<script setup lang="ts">
import DialogApi from "@/components/dialog-api.vue";
import MessageApi from "@/components/message-api.vue";
import Dashboard from "@/components/dashboard.vue";
import { darkTheme, lightTheme, useOsTheme } from 'naive-ui';
import type { BuiltInGlobalTheme } from "naive-ui/es/themes/interface";
import { post } from "@/core/axios";
import Login from "@/components/login.vue";
import Register from "@/components/register.vue";
import { useUserStore } from "@/stores/user";
import useClipboard from 'vue-clipboard3';
import Evt from "@/core/evt";
import Handler from "@/core/Handler";
import { STATE, StorageKey } from "@/core/z";

// import {handleNotification} from "@/core/notification";

const { toClipboard } = useClipboard();

const osTheme = useOsTheme();
const userStore = useUserStore()

const theme = ref<BuiltInGlobalTheme | null>(null);


Evt.on("THEME_CHANGE", Handler.alloc(null, (themeType: string) => {
  const isDark = themeType === 'dark';
  theme.value = isDark ? darkTheme : null;
  // 通知game  主题改变
  const item = localStorage.getItem(StorageKey.DASHBOARD_DATA);
  if (item) {
    let arr = JSON.parse(item);
    arr = arr.sort((a: any, b: any) => a.sort - b.sort)
    arr.forEach((item: any) => {
      try {
        window.GAME_EXEC(item.id, `Evt.emit("THEME_CHANGE", '${themeType}');`)
      } catch (e) {
        // ignore
      }
    })
  }

}), false)

// 监听主题变化
watch(
  osTheme,
  newValue => {
    // 不需要跟随系统主题
    if (!userStore.themeAuto) return
    Evt.emit("THEME_CHANGE", newValue);
  },
  { immediate: true }
);

// window["handleNotification"] = handleNotification

window.Events = window.Events || {};
window.postReq = post
window.getMachine = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!window.DASHBOARD_REQ_GET_MACHINE) {
      let times = 0
      let interval = setInterval(() => {
        if (times >= 21) return reject("获取设备码超时！")
        if (window.DASHBOARD_REQ_GET_MACHINE) {
          clearInterval(interval)
          resolve(window.DASHBOARD_REQ_GET_MACHINE())
        }
        times += 1
      }, 100);
      return
    } else {
      resolve(window.DASHBOARD_REQ_GET_MACHINE())
    }
  })
}

//@ts-ignore
window.gen = (cnt: number, key: string, permission: { channel: number, has: boolean }[], changeCnt: number) => {
  if (cnt <= 0 || key == "") return
  console.log(`生成个数：${cnt}, 变更次数：${changeCnt},权限数据: ${JSON.stringify(permission)}`)
  console.log("方法已经生成，执行调用即可。")
  return async () => {
    const data = await window.postReq<any>("/genSign", { key, cnt, changeCnt, permission })
    console.log("@@test@@: ", data)
  }
}
const copyBtn = ref<any>(null);
window.copy = (str: string, el?: any) => {
  const copyText = async (text: string) => {
    try {
      if (el)
        await toClipboard(text, el);
      else
        await toClipboard(text);
      window.$message.success("复制成功!")
    } catch (e) {
      console.error(e);
    }
  }
  copyText(str)
}

window.wait = (delay: number) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => resolve(0), delay)
  })
}

onMounted(() => {
  // 加载主题参数
  let themeAuto = localStorage.getItem("_is_theme_auto")
  let themeType = localStorage.getItem("_theme_type")
  if (themeAuto == null || themeType == null) {
    localStorage.setItem("_is_theme_auto", "0")
    localStorage.setItem("_theme_type", "dark")
    userStore.themeAuto = false
    userStore.themeType = false
    Evt.emit("THEME_CHANGE", 'dark');
  } else {
    userStore.themeAuto = themeAuto == "1"
    userStore.themeType = themeType == "light"
    // theme.value = themeType == "light" ? undefined : darkTheme
    Evt.emit("THEME_CHANGE", themeType);
  }
  // 提供给game获取
  window.GetThemeAuto = () => userStore.themeAuto
  window.GetThemeType = () => userStore.themeType


  // http请求封装
  window.request = async (url: string) => {
    return new Promise((resolve, reject) => {
      fetch(url).then(async (data) => {
        if (data.status !== 200) {
          reject(data.statusText);
          return void window.$message.error("资源下载失败!请重启后重试", { duration: 10000 })
        }
        const reader = data.body?.['getReader']();
        const content = [];
        let done = false;
        while (!done) {
          // @ts-ignore
          const { value, done: isDone } = await reader?.read();
          if (value) {
            content.push(value);
          }
          done = isDone;
        }
        const concatenated = new Uint8Array(content.reduce((acc, chunk) => acc.concat(Array.from(chunk)), []));
        //@ts-ignore
        return btoa(String.fromCharCode.apply(null, concatenated));
      }).then(ret => {
        resolve(ret);
      });
    })
  }
  window.Events["DOWNLOAD_FILE"] = async (url: string) => {
    return await window.request(url);
  }

})

</script>
