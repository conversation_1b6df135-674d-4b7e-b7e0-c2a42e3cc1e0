<template>
  <div></div>
</template>

<script lang="ts" setup>
import {darkTheme, useDialog, useDialogReactiveList, useOsTheme} from "naive-ui";
import ButtonGroup from "@/components/button-group/index.vue";
import Evt from "@/core/evt";
import Handler from "@/core/Handler";

const osTheme = useOsTheme();
const isDark = ref(osTheme.value === 'dark');
Evt.on("THEME_CHANGE", Handler.alloc(null, (themeType: string) => {
  isDark.value = themeType === 'dark';
}),false)

watchEffect(() => {
  document.documentElement.style.setProperty(
      '--dialog-bg-color',
      isDark.value ? 'rgba(33, 32, 32, 0.63)' : 'rgba(255,255,255,0.6)'
  );
  document.documentElement.style.setProperty(
      '--button-group-border-color',
      isDark.value ? 'hsla(240, 1%, 72%, 0.25)' : 'hsla(240,2%,18%,0.25)'
  );
  // button-group的按钮停留颜色
  document.documentElement.style.setProperty(
      '--button-group-hover-color',
      isDark.value ? 'hsla(240, 1%, 72%, 0.2)' : 'hsla(0,0%,6%,0.2)'
  );
});

window.$dialog = useDialog();
window.$dialogReactive = useDialogReactiveList()

const dialogList = useDialogReactiveList()
// 监听对话框，同时只能存在一个对话框
watch(
    dialogList.value,
    newValue => {
      for (let i = 0; i < newValue.length - 1; i++) {
        newValue[i].destroy()
      }
    },
    {immediate: true}
);

type notifyArgs = {
  title: string,
  type: string,
  msg: string,
  maskClosable: boolean,
  action: Array<{ label: string, type: string, callback: string }>
}

window.sampleNotify = (args: string) => {
  let arg: notifyArgs = null
  try {
    arg = JSON.parse(args)
  } catch (e) {
    return
  }
  if (!arg) return

  typeof arg.type === "undefined" && (arg.type = "info")
  typeof arg.maskClosable === "undefined" && (arg.maskClosable = true)
  if (arg.action && arg.action.length) {
    arg.action.forEach(act => {
      const str = act.callback
      if (str) {
        act.callback = () => eval(str)
      }
    })
  } else {
    // 补一个关闭按钮
    arg.action = [{label: "关闭", type: "default", callback: "dlg.destroy()"}]
  }
  window.dlg = window.$dialog[arg.type]({
    title: arg.title || "",
    closable: false,
    maskClosable: arg.maskClosable,
    content: () => arg.msg || "",
    action: () => h(ButtonGroup, {
      action: arg.action
    })
  })
}

</script>

<style>

.n-modal-container {
  z-index: 2047 !important;
}

.n-drawer-container {
  z-index: 2048 !important;
}

.n-dialog {
  border-radius: 25px;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  padding: 0 !important;
  background-color: var(--dialog-bg-color, rgba(255, 255, 255, 0.5));
}

.n-dialog__title {
  justify-content: center;
  letter-spacing: 2px;
  padding: 20px;
}

.n-dialog__content {
  text-align: center;
  letter-spacing: 2px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  align-content: center;
}

</style>
