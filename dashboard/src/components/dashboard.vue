<template>
  <div class='loading' v-if="loading">
    <n-button :loading="loading" type="default" secondary>{{ loadingText }}</n-button>
  </div>
  <download v-if="false" class="loading"/>
  <tip :add-event="handleAdd" :my-event="handleMy" :set-event="handleSet"/>
  <div id="container">
    <div id="list">
      <div v-for="(item,index) in data" draggable="true" class="list-item" :sort="item.sort">
        <img draggable="false" :src="Channels[item.channel].icon" class="channel-icon"/>
        <n-ellipsis draggable="false" style="width: 100px">{{ item.name }}</n-ellipsis>
        <div draggable="false" class="operations">
          <NButton size="tiny" type="info" :secondary='true' @click="handleRename(item)">改名</NButton>
          <NButton size="tiny" type="success" :secondary='true' @click="handleLaunch(item)"
                   :loading="item.state == STATE.STARTING"
                   :disabled="item.state == STATE.RUNNING">启动
          </NButton>
          <NButton size="tiny" type="warning" :secondary='true' @click="handleStop(item)"
                   :loading="item.state == STATE.STOPPING"
                   :disabled="item.state != STATE.RUNNING">停止
          </NButton>
          <NButton size="tiny" type="error" :secondary='true' @click="handleDelete(item)"
                   :disabled="item.state != STATE.STOPPED">删除
          </NButton>
          <NButton size="tiny" type="default" :secondary='true' @click="handleSetGame(item)"
                   :disabled="item.channel == ChannelType.NONE">
            设置
          </NButton>
        </div>
      </div>
    </div>
    <div v-if="need_empty" draggable="false"
         style="display: flex;height: 120px;user-select: none;flex-direction: column;align-items: center;justify-content: flex-start">
      <n-gradient-text gradient="linear-gradient(135deg,#536976,#292e49)">
        按住移动可以进行拖动排序哦~
      </n-gradient-text>
      <n-gradient-text gradient="linear-gradient(135deg,#536976,#292e49)">
        F5可以刷新页面~
      </n-gradient-text>
      <n-gradient-text gradient="linear-gradient(135deg,#000c40,#607d8b)">
        ————————————我是有底线的————————————
      </n-gradient-text>
    </div>
  </div>
</template>

<script setup lang="ts">

import type {Record} from "@/core/z";
import {Channels, ChannelType, STATE, StorageKey, ZoomMap} from "@/core/z";
import {NButton, NTooltip, NSelect, NInput, NText, NIcon, NSpace} from "naive-ui";
import ButtonGroup from "./button-group/index.vue";
import Handler from "@/core/Handler";
import Evt from "@/core/evt";
import setting from "@/components/setting.vue";
import my from "@/components/my.vue";
import gameSetting from "@/components/game-setting.vue";
import tip from "@/components/tip.vue";
import download from "@/components/download.vue";

import {Flip} from "@/core/Flip";
import {useUserStore} from "@/stores/user";
import {useKeyStore} from "@/stores/keys";
import type {VNode} from "vue";
import Download from "@/components/download.vue";
import {Checker} from "@/core/team";

const user = useUserStore()
const key = useKeyStore()
key.initKeyData()

const loading = ref(false)
const loadingText = ref("")

//重载页面
const EVT_RELOAD = Handler.alloc(null, () => {
  const dlg = window.$dialog.info({
    title: "刷新页面？",
    closable: false,
    content: () => "",
    action: () => h(ButtonGroup, {
      action: [
        {label: "确定", type: "success", callback: () => location.reload()},
        {label: "取消", type: "default", callback: () => dlg.destroy()},
      ]
    })
  })
})

// 事件列表
Evt.on("EVT_RELOAD", EVT_RELOAD,true)

const data = ref<Record[]>([])
const tempName = ref("")
const handleRename = (row: Record) => {
  tempName.value = row.name;
  const dlg = window.$dialog.info({
    title: "配置重命名",
    closable: false,
    content: () => h(NInput, {
      size: "tiny",
      style: {
        width: '60%'
      },
      clearable: true,
      value: tempName.value,
      onInput: (e) => {
        tempName.value = e
      }
    }),
    action: () => h(ButtonGroup, {
      action: [
        {label: "确定", type: "success", callback: () => doRename(row, dlg)},
        {label: "关闭", type: "default", callback: () => dlg.destroy()},
      ]
    })
  })

}

const doRename = (row: Record, dlg: any) => {
  if (tempName.value == "") {
    return void window.$message.error("名称不要填写为空哦~")
  }
  window.$message.success("修改完成~")
  if (tempName.value == row.name) return void dlg.destroy()
  row.name = tempName.value;
  handleSave()
  return void dlg.destroy()
}

const defaultChannel = ChannelType.NONE
const selectChannel = ref(ChannelType.NONE)

type optionsType = { label: string, value: number, icon: string }
const handleLaunch = async (row: Record) => {
  selectChannel.value = row.channel
  const launch = async (dlg: any) => {
    if (selectChannel.value == defaultChannel) {
      return void window.$message.error("不可以选择未知版本启动！", {duration: 1000})
    }
    dlg && dlg.destroy()
    row.channel = selectChannel.value
    handleSave()
    // 进入启动状态
    row.state = STATE.STARTING
    await window.DASHBOARD_REQ_START_GAME(row.id, Channels[row.channel].url, row.channel + "", row.name || "")
  }

  // 如果没有选择过渠道，就先让其选择渠道
  if (row.channel == ChannelType.NONE) {
    const options: optionsType[] = []
    Object.keys(ChannelType).forEach((key) => {
      const idx = Number(key)
      if (isNaN(idx)) return
      options.push({label: Channels[idx].name, value: idx, icon: Channels[idx].icon})
    })

    const dlg = window.$dialog.info({
      title: "选择版本",
      closable: false,
      content: () => h(NSelect, {
        value: selectChannel.value,
        options,
        renderOption: ({node, option, selected}: {
          node: VNode,
          option: optionsType,
          selected: boolean
        }) => h(NTooltip, {placement: 'right',}, {
          trigger: () => node,
          default: () => user.getCanLaunch(option.value) ? "可以启动" : "无权限"
        }),
        renderLabel: (option: optionsType) => h(NSpace, {}, {
          default: () => [
            h(NIcon, {}, {
              default: () => h('img', {
                src: option.icon,
                style: {width: "18px", height: "18px", borderRadius: "50%"}
              })
            }),
            option.label as string
          ]
        }),
        style: {
          width: "160px",
        },
        onUpdateValue: (v) => {
          if (user.getCanLaunch(v)) {
            selectChannel.value = v
            return
          }
          window.$message.error(`您的账号无权限启动该版本:${options[v].label}`)
        }
      }),
      action: () => h(ButtonGroup, {
        action: [
          {label: "确定", type: "success", callback: () => launch(dlg)},
        ]
      })
    })
    return
  }
  launch(null)
}

const handleStop = (row: Record) => {
  // 进入停止状态
  row.state = STATE.STOPPING
  window.DASHBOARD_REQ_STOP_GAME(row.id)
}

const handleDelete = (row: Record) => {
  const dlg = window.$dialog.warning({
    title: "删除配置",
    closable: false,
    content: () => "删除配置后不可复原，请注意您的账号和密码保存情况!",
    action: () => h(ButtonGroup, {
      action: [
        {label: "确定", type: "success", callback: () => doDelete(row, dlg)},
        {label: "算了", type: "default", callback: () => dlg.destroy()},
      ]
    })
  })
}

const doDelete = (row: Record, dlg: any) => {
  window.$message.success("删除成功~")
  const idx = data.value.findIndex((item) => item == row)
  const item = data.value.splice(idx, 1)
  window.DASHBOARD_REQ_DEL_GAME(item[0].id)
  unListenLaunchEvent(row)
  unListenStopEvent(row)
  // 移除事件
  delete window.Events[`_launch_result_${item[0].id}`]
  delete window.Events[`_stop_result_${item[0].id}`]
  handleSave()
  if (data.value.length <= 12) {
    need_empty.value = false
  }
  return void dlg.destroy()
}
const need_empty = ref(false)
const default_record = {
  id: "",
  sort: -1,
  name: "默认配置",
  state: STATE.STOPPED,
  channel: ChannelType.NONE
}

// dashboard loading
// Evt.emit("EVT_DASHBOARD_LOADING",true)
// Evt.emit("EVT_DASHBOARD_LOADING",false)
const EVT_DASHBOARD_LOADING = Handler.alloc(null, (ing: boolean, text: string) => {
  loading.value = ing
  loadingText.value = text || ""
  if (!ing) {
    loadingText.value = ""
  }
})

// 事件列表
Evt.on("EVT_DASHBOARD_LOADING", EVT_DASHBOARD_LOADING,true)


const handleAdd = () => {
  Evt.emit("EVT_DASHBOARD_LOADING", true, "请稍等")
  const __ = JSON.parse(JSON.stringify(default_record))
  __.id = Date.now() + "";
  __.sort = data.value.length
  data.value.push(__);
  handleSave()
  data.value = [];
  setTimeout(() => {
    window.$message.info("添加成功", {duration: 1000})
    handleLoad()
    Evt.emit("EVT_DASHBOARD_LOADING", false)
  }, Math.floor(Math.random() * (800 - 300) + 300))
}

const handleSave = () => {
  localStorage.setItem(StorageKey.DASHBOARD_DATA, JSON.stringify(data.value));
}

// 延迟同步状态
const delayGetGameSid = ref(-1)
const delayGetGameState = () => {
  if (delayGetGameSid.value) return
  delayGetGameSid.value = setInterval(() => {
    if (typeof window.DASHBOARD_REQ_GET_GAME !== "function") return
    data.value.forEach(async (item) => {
      const str = window.DASHBOARD_REQ_GET_GAME(item.id)
      item.state = str == "1" ? STATE.RUNNING : STATE.STOPPED
    })
    clearInterval(delayGetGameSid.value)
    delayGetGameSid.value = -1
  }, 1000)
}

const handleLoad = () => {
  const item = localStorage.getItem(StorageKey.DASHBOARD_DATA);
  if (item) {
    const arr = JSON.parse(item);
    data.value = arr.sort((a: Record, b: Record) => a.sort - b.sort)
    data.value.forEach(async (item) => {
      item.state = STATE.STOPPED
      listenLaunchEvent(item)
      listenStopEvent(item)
    })
    if (data.value.length > 12) {
      need_empty.value = true
    }
  }
  delayGetGameState()
}
// 启动事件
const unListenLaunchEvent = (row: Record) => {
  delete window.Events[`_launch_result_${row.id}`]
}
const listenLaunchEvent = (row: Record) => {
  unListenLaunchEvent(row)
  // 注册一个启动后的通知回调
  window.Events[`_launch_result_${row.id}`] = (result: number) => {
    switch (result) {
      case -1:
        row.state = STATE.STOPPED
        window.$message.error("出错啦,启动失败~", {duration: 1000})
        break;
      case 0:
        row.state = STATE.RUNNING
        window.$message.success(`启动${row.name}成功啦~`, {duration: 1000})
        break;
    }
    return ""
  }
}
// 停止事件
const unListenStopEvent = (row: Record) => {
  delete window.Events[`_stop_result_${row.id}`]
}
const listenStopEvent = (row: Record) => {
  unListenStopEvent(row)
  // 注册一个停止后的通知回调
  window.Events[`_stop_result_${row.id}`] = (result: number) => {
    switch (result) {
      case -1:
        row.state = STATE.RUNNING
        window.$message.error("出错啦,停止实例失败~", {duration: 1000})
        break;
      case -2: // 找不到实例？？
      case 0:
        row.state = STATE.STOPPED
        window.$message.success(`停止${row.name}成功~`, {duration: 1000})
        break;
      case 1:
        // game主动停止
        row.state = STATE.STOPPED
        break;
    }
    return ""
  }
}

let flip: Flip = null;
onMounted(() => {
  handleLoad();
  // 注册一个获取配置的安全锁密码方法
  window.Events[`__get_safe_password`] = (id: string) => {
    if (!id || !data.value.length) return ""
    const find = data.value.find((item) => item.id == id);
    return find ? find.password || "" : "";
  }
  // 滚动事件
  const list = document.getElementById("list")
  let sourceNode: HTMLElement | null = null;
  if (list != null) {
    let sid = -1
    let call: Function | null
    list.ondragstart = (ev: DragEvent) => {
      let target = ev.target as HTMLElement
      if (!target.classList.contains("list-item")) {
        const children: Element[] = [...list.children]
        for (let i = 0; i < children.length; i++) {
          const child = children[i]
          if (child.contains(target)) {
            target = child as HTMLElement
            break
          }
        }
      }
      setTimeout(() => {
        target.classList.add("moving")
      }, 0)
      if (ev.dataTransfer) {
        ev.dataTransfer.effectAllowed = "move"
      }
      sourceNode = target
      flip = new Flip(list.children, .3)
    }

    list.ondragover = (ev: DragEvent) => {
      ev.preventDefault()
    }
    list.ondragenter = (ev: DragEvent) => {
      try {
        ev.preventDefault();
        let target = ev.target as HTMLElement
        if (target == list || target == sourceNode) return;
        const children: Element[] = [...list.children]
        const sourceIndex = children.indexOf(sourceNode as HTMLElement)
        let targetIndex = -1
        if (target.classList.contains("list-item")) {
          targetIndex = children.indexOf(target)
        } else {
          for (let i = 0; i < children.length; i++) {
            const child = children[i]
            if (child.contains(target)) {
              targetIndex = i
              target = child as HTMLElement
              break
            }
          }
        }
        if (targetIndex == -1) return
        if (sourceIndex < targetIndex) {
          list.insertBefore(sourceNode as HTMLElement, target.nextElementSibling)
        } else {
          list.insertBefore(sourceNode as HTMLElement, target)
        }
        flip.play()
      } catch (e) {
      }
    }
    list.ondragend = (ev: DragEvent) => {
      if (ev.target instanceof HTMLElement) {
        ev.target.classList.remove("moving")
      }
      call && call()
      call = () => {
        sid > -1 && clearTimeout(sid)
        const children: Element[] = [...list.children]
        const exclude: Record[] = new Array(data.value.length)
        for (let i = 0; i < children.length; i++) {
          //@ts-ignore
          const sort = parseInt(children[i].attributes.sort.value)
          if (data.value[sort].sort != i) {
            // console.log(data.value[sort].name, `${data.value[sort].sort} -> ${i}`)
          }
          const il = data.value.filter((item) => item.sort == sort).filter((item) => !exclude.includes(item))
          if (il.length != 1) console.error("数据错误", il, i)
          il[0].sort = i
          exclude.push(il[0])
        }
        // console.log(data.value)
        // console.log(list.children)
        call = null
        handleSave()
      }

      sid = setTimeout(call, 320)
    }
  }
})

const handleSet = () => {
  const dlg = window.$dialog.info({
    title: "",
    closable: false,
    maskClosable: false,
    closeOnEsc: false,
    showIcon: false,
    autoFocus: false,
    content: () => h(setting, {}),
    action: () => h(ButtonGroup, {
      action: [
        {label: "关闭", type: "default", callback: () => dlg.destroy()},
      ]
    })
  })
}

const zoomRef = ref(1.0)
const passwordRef = ref("")
const autoGameLimitTimeRef = ref(200)
const netWaitTimeRef = ref(10000)
const battlePoolIntervalRef = ref(500)
const handleSetGame = (row: Record) => {
  passwordRef.value = row.password || ""
  let val = Number(localStorage.getItem(`game_zoom_${row.id}`) || '1')
  if (val) {
    if (val > 4) val = 1
    zoomRef.value = val
  }
  const autoGameLimitTimeVal = Number(localStorage.getItem(`game_auto_game_limit_time_${row.id}`) || '200')
  if (autoGameLimitTimeVal) {
    autoGameLimitTimeRef.value = autoGameLimitTimeVal
  }
  const netWaitTimeVal = Number(localStorage.getItem(`net_wait_time_${row.id}`) || '10000')
  if (netWaitTimeVal) {
    netWaitTimeRef.value = netWaitTimeVal
  }
  const battlePoolIntervalVal = Number(localStorage.getItem(`battle_pool_interval_${row.id}`) || '500')
  if (battlePoolIntervalVal) {
    battlePoolIntervalRef.value = battlePoolIntervalVal
  }
  const dlg = window.$dialog.info({
    title: "设置",
    closable: false,
    maskClosable: false,
    closeOnEsc: false,
    showIcon: false,
    autoFocus: false,
    content: () => h(gameSetting, {
      passwordRef,
      zoomRef,
      autoGameLimitTimeRef,
      netWaitTimeRef,
      battlePoolIntervalRef,
      row,
      channel: row.channel
    }),
    action: () => h(ButtonGroup, {
      action: [
        {
          label: "关闭", type: "default", callback: () => {
            if (passwordRef.value != row.password) {
              row.password = passwordRef.value
              handleSave()
            }
            if (autoGameLimitTimeRef.value != autoGameLimitTimeVal) {
              localStorage.setItem(`game_auto_game_limit_time_${row.id}`, autoGameLimitTimeRef.value + "")
            }
            if (netWaitTimeRef.value != netWaitTimeVal) {
              localStorage.setItem(`net_wait_time_${row.id}`, netWaitTimeRef.value + "")
            }
            if (battlePoolIntervalRef.value != battlePoolIntervalVal) {
              localStorage.setItem(`battle_pool_interval_${row.id}`, battlePoolIntervalRef.value + "")
            }
            dlg.destroy()
          }
        },
      ]
    })
  })
}
/**
 * 注册一个获取缩放比例配置的事件
 * @param id
 * @constructor
 */
window.Events["GetZoom"] = (id: string) => {
  const val = localStorage.getItem(`game_zoom_${id}`)
  return val || "1";
}

const handleMy = () => {
  const dlg = window.$dialog.info({
    title: "",
    closable: false,
    maskClosable: false,
    closeOnEsc: false,
    showIcon: false,
    autoFocus: false,
    content: () => h(my, {}),
    action: () => h(ButtonGroup, {
      action: [
        {label: "关闭", type: "default", callback: () => dlg.destroy()},
      ]
    })
  })
}

window.subscribeTeam = (leaderId: number, selfId: number, gameId: string) => Checker.subscribeTeam(leaderId, selfId, gameId)
window.unSubscribeTeam = (selfId: number, gameId: string) => Checker.unSubscribeTeam(selfId, gameId)
window.sendTeamMsg = (leaderId: number, sender: number, gameId: string, msg: string) => Checker.onTeamMsg(leaderId, sender, gameId, msg)

</script>

<style scoped>

* {
  user-select: none
}

#container {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}

.bottom {
  position: fixed;
  bottom: 10px;
  left: 1vw;
  height: 34px;
  width: 98vw;
  transition: .5s;
  border-radius: 34px;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background-color: rgba(33, 32, 32, 0.5);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  color: white !important;
  padding: 0 !important;
  overflow: hidden;
  display: flex;
}

.bottom:hover {
}

.bnt-container {
  width: 50%;
  height: 100%;
  background-color: transparent;
  text-align: center;
  font-weight: bolder;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  transition: .5s;
}

.bnt-container:first-child {
  border-right: 1px rgba(255, 255, 255, 0.4) solid;
}

.bnt-container:first-child:hover {
  color: #ffa502;
  background-color: rgba(255, 165, 2, .1);
}

.bnt-container:last-child:hover {
  color: #9b59b6;
  background-color: rgba(142, 68, 173, 0.1);
}

.bnt-container:last-child {
  border-left: 1px rgba(255, 255, 255, 0.4) solid;
}

.bnt-container:hover {
  color: #ff4757;
  background-color: rgba(255, 107, 129, 0.1);
}

.list-item {
  width: 100%;
  height: 36px;
  line-height: 36px;
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-evenly;
  border-bottom: 1px solid rgba(20, 18, 18, 0.1);
}

.list-item.moving {
  background: transparent;
  color: transparent;
  border: none;
}

.channel-icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.operations {
  display: flex;
  justify-content: flex-start;
  gap: 4px
}

.loading {
  height: 100vh;
  width: 100vw;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 997;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background-color: rgba(33, 32, 32, 0.5);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.loading .n-button {
  width: 100vw;
  height: 100vh;
  background-color: transparent;
  border: 0;
  box-shadow: none;
  color: #78e08f;
  font-size: 20px;
  font-weight: bolder;
  padding: 0;
  margin: 0;
}

</style>
