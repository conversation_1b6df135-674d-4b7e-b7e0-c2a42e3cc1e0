<template>
  <div class="container bottom-left-radius bottom-right-radius" :class="props.action.length > 0 ? 'cs-t0' : ''"
       style="width: 100%">
    <!--    <div v-if="props.action.length > 2 || props.action.length === 1" v-for="(act, i) in props.action"-->
    <!--         class="button-group" :class="getClazz(act, i)"-->
    <!--         :disabled="act.disable"-->
    <!--         @click="act.callback">-->
    <!--      {{ act.label }}-->
    <!--    </div>-->

    <n-button quaternary v-for="(act, i) in props.action"
              class="button-group"
              :class="getClazz(act, i)"
              :type="act.type || 'default'"
              :disabled="act.disable"
              @click="act.callback">
      {{ act.label }}
    </n-button>

    <!--    <div v-else-if="props.action.length === 2">-->

    <!--      <n-button quaternary class="button-group button-group-half cs-t2"-->
    <!--                :disabled="!!props.action[0].disable"-->
    <!--                :type="props.action[0].type"-->
    <!--                :class="getClazz(props.action[0], 0)" @click="props.action[0].callback">-->
    <!--        {{ props.action[0].label }}-->
    <!--      </n-button>-->
    <!--      <n-button quaternary class="button-group button-group-half cs-t2"-->
    <!--                :disabled="!!props.action[1].disable"-->
    <!--                :type="props.action[0].type"-->
    <!--                :class="getClazz(props.action[1], 1)" @click="props.action[1].callback">-->
    <!--        {{ props.action[1].label }}-->
    <!--      </n-button>-->

    <!--      <div class="button-group button-group-half cs-t2"-->
    <!--           :class="getClazz(props.action[0], 0)" @click="props.action[0].callback">-->
    <!--        {{ props.action[0].label }}-->
    <!--      </div>-->
    <!--      <div class="button-group button-group-half cs-t1"-->
    <!--           :class="getClazz(props.action[1], 1)" @click="props.action[1].callback">-->
    <!--        {{ props.action[1].label }}-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script lang="ts" setup>

import {onMounted} from "vue";
import {useOsTheme} from "naive-ui";
import Evt from "@/core/evt";
import Handler from "@/core/Handler";

const types = ["default", "tertiary", "primary", "success", "info", "warning", "error"]

type ActionType = "default" | "tertiary" | "primary" | "success" | "info" | "warning" | "error"
type Action = {
  label: string
  type: ActionType
  disable?: boolean
  callback?: (payload: MouseEvent) => void
}

interface Props {
  action: Action[]
}

const props = withDefaults(defineProps<Props>(), {})
onMounted(() => {

})

const getClazz = (act: Action, index: number) => {
  let clazz = ""
  if (!props.action) return clazz
  // 如果是最后一个按钮，需要补齐下方的圆角
  if ((props.action.length > 2 && index == props.action.length - 1) || props.action.length == 1) {
    clazz += " bottom-left-radius bottom-right-radius"
  }
  if (props.action.length == 2) {
    clazz += index ? " bottom-right-radius" : " bottom-left-radius"

    index === 0 && (clazz += ` cs-t1`)
    // index === 1 && (clazz += ` cs-t2`)
  } else if (index > 0) {
    clazz += " cs-t0"
  }

  if (props.action.length === 2) {
    clazz += " button-group-half"
  }

  // types.indexOf(act.type) != -1 && (clazz += " " + act.type)
  return clazz
}

const osTheme = useOsTheme();
const isDark = ref(osTheme.value === 'dark');
Evt.on("THEME_CHANGE", Handler.alloc(null, (themeType: string) => {
  isDark.value = themeType === 'dark';
}),false)

watchEffect(() => {
  document.documentElement.style.setProperty(
      '--default-btn-color',
      isDark.value ? 'rgb(255,252,252)' : '#060606'
  );
});

</script>

<style scoped>

.container {
  overflow: hidden;
}

.bottom-left-radius {
  border-bottom-left-radius: 20px;
}

.bottom-right-radius {
  border-bottom-right-radius: 20px;
}


.button-group {
  text-align: center;
  padding: 12px;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  transition-property: background;
  transition-duration: .5s;
  height: 48px;
  border-radius: 0;
}

.button-group-half {
  width: calc(100% / 2) !important;
  float: left;
}


.button-group:hover {
  /*background-color: var(--button-group-hover-color, hsla(240, 1%, 72%, 0.1));*/
}

.cs-full-width {
  width: 100%;
}

.cs-t0 {
  border-top: 1px solid var(--button-group-border-color, hsla(240, 1%, 72%, 0.25)) !important;
}

.cs-t1 {
  border-right: 1px solid var(--button-group-border-color, hsla(240, 1%, 72%, 0.25)) !important;
}

.cs-t2 {
  border-left: 1px solid var(--button-group-border-color, hsla(240, 1%, 72%, 0.25)) !important;
}

/*.info {*/
/*  color: #2080F0 !important;*/
/*}*/

/*.warning {*/
/*  color: #F0A020 !important;*/
/*}*/

/*.error {*/
/*  color: #D02F50 !important;*/
/*}*/

/*.success {*/
/*  color: #19A058 !important;*/
/*}*/

/*.default {*/
/*  color: var(--default-btn-color, rgba(255, 255, 255, 0.35));*/
/*}*/

</style>
