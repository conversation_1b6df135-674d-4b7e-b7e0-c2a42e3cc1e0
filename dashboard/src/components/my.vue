<script setup lang="ts">

import {useUserStore} from "@/stores/user";

const user = useUserStore()

const copy = (e) => {
  window.copy(user.userModel.sign, e.target)
}

</script>

<template>
  <div>
    <n-grid x-gap="2" y-gap="2" :cols="1">
      <n-gi>
        <n-space>
          <n-text>注册码</n-text>
          <n-button size="tiny" type="error" secondary @click="copy">**点击复制**
          </n-button>
        </n-space>
      </n-gi>
      <n-gi>
        <n-space>
          <n-text>用户名</n-text>
          <n-button size="tiny" type="error" secondary @click="">{{ user.userModel.username }}</n-button>
        </n-space>
      </n-gi>
      <n-gi>
        <n-tooltip trigger="hover" :style="{fontSize:'12px'}">
          <template #trigger>
            <n-space>
              <n-text>设备码</n-text>
              <n-button size="tiny" type="error" secondary @click="user.unbindRequest">已绑定：{{
                  user.userModel.machine
                }}， {{ user.userModel.todayChangeMachineCnt || 0 }}次
              </n-button>
            </n-space>
          </template>
          变更登录设备时，会消耗一次绑定机会。
          <br/>
          当前剩余<span style="color:red;">{{user.userModel.todayChangeMachineCnt}}次</span>。
        </n-tooltip>
      </n-gi>
      <n-gi>
        <n-space>
          <n-text>已登录</n-text>
          <n-button size="tiny" type="error" secondary @click="user.offlineRequest">退出登录</n-button>
        </n-space>
      </n-gi>
    </n-grid>
  </div>
</template>

<style scoped>

</style>
