<template>
  <div style="padding: 4px;">
    <n-grid x-gap="2" y-gap="2" :cols="24">
      <n-gi span="8">
        <div class="desc-right">缩放比例</div>
      </n-gi>
      <n-gi span="12">
        <n-tooltip trigger="hover" :style="{fontSize:'12px'}">
          <template #trigger>
            <n-input-number v-model:value="props.zoomRef.value" :precision="1" :step="0.1" size="tiny" :min="1.0"
                            :max="4.0"/>
          </template>
          范围1.0-4.0，越高越吃显卡画面则越清晰。
          <br/>
          需要重启游戏。
        </n-tooltip>
      </n-gi>
      <n-gi span="4">
        <n-button size="tiny" type="warning" @click="() => {zoomRef.value = 1.0}">重置</n-button>
      </n-gi>

      <n-gi span="8" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <div class="desc-right">解锁安全锁</div>
      </n-gi>
      <n-gi span="16" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <n-tooltip trigger="hover" :style="{fontSize:'12px'}">
          <template #trigger>
            <n-input v-model:value="props.passwordRef.value" size="tiny" placeholder="登录后会自动解锁"/>
          </template>
          角色进入游戏后，自动解锁安全锁。
          <br/>
          如果有设置安全锁的话。
        </n-tooltip>
      </n-gi>

      <n-gi span="8" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <div class="desc-right">逻辑加速</div>
      </n-gi>
      <n-gi span="16" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <n-tooltip trigger="hover" :style="{fontSize:'12px'}">
          <template #trigger>
            <n-input-number v-model:value="props.battlePoolIntervalRef.value" size="tiny" :show-button="false"
                            :min="200"
                            :max="2000"/>
          </template>
          这个值影响游戏整体速度，默认500ms。
          <br/>
          范围是200-2000，500已经够了，不要调太低了。
        </n-tooltip>
      </n-gi>

      <n-gi span="8" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <div class="desc-right">自动任务速度</div>
      </n-gi>
      <n-gi span="16" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <n-tooltip trigger="hover" :style="{fontSize:'12px'}">
          <template #trigger>
            <n-input-number v-model:value="props.autoGameLimitTimeRef.value" size="tiny" :show-button="false" :min="200"
                            :max="2000" placeholder="默认是200ms"/>
          </template>
          自动任务的速度，200~2000，单位毫秒。
          <br/>
          默认是200ms。
        </n-tooltip>
      </n-gi>

      <n-gi span="8" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <div class="desc-right">网络读条时长</div>
      </n-gi>
      <n-gi span="16" v-if="props.channel == ChannelType.TIAN_YU || props.channel == ChannelType.GUAN_FANG">
        <n-tooltip trigger="hover" :style="{fontSize:'12px'}">
          <template #trigger>
            <n-input-number v-model:value="props.netWaitTimeRef.value" size="tiny" :show-button="false" :min="3000"
                            :max="30000"/>
          </template>
          网络读条超时后会自动关闭。
          <br/>
          默认是10000ms即10秒，范围3-30s。
        </n-tooltip>
      </n-gi>

    </n-grid>
  </div>
</template>
<script setup lang="ts">

import type {Ref} from "vue";
import type {Record} from "@/core/z";
import {ChannelType, STATE, ZoomMap} from "@/core/z";

interface Props {
  passwordRef: Ref<string>
  zoomRef: Ref<number>
  autoGameLimitTimeRef: Ref<number>
  netWaitTimeRef: Ref<number>
  battlePoolIntervalRef: Ref<number>
  row: Record
  channel: ChannelType
}

const props = withDefaults(defineProps<Props>(), {})

const getZoomLevel = () => {

}

watch(
    props.zoomRef,
    (newValue) => {
      if (props.row.state !== STATE.RUNNING) {
        localStorage.setItem(`game_zoom_${props.row.id}`, newValue + "")
      } else {
        const func = window.DASHBOARD_REQ_SET_ZOOM
        if (!func) return
        const res = func(props.row.id, newValue)
        if (res == "ok") {
          localStorage.setItem(`game_zoom_${props.row.id}`, newValue + "")
        }
      }
    },
    {immediate: true}
)

</script>
<style scoped>

.desc-right {
  text-align: right;
  width: 100%
}

</style>
