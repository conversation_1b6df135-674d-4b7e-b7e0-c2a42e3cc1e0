<script setup lang="ts">

import {useUserStore} from "@/stores/user";

const user = useUserStore()

interface Props {

}

const props = withDefaults(defineProps<Props>(), {})

const percentage = ref(0)
const speedVal = ref(0)
const status = ref('default')
const interruptedReason = ref('')
onMounted(() => {
  window.DownloadFinished = () => {
    status.value = "success"
  }

  window.DownloadUpdated = (progress: number, currentSpeed: number, receivedBytes: number, totalBytes: number) => {
    speedVal.value = Number((currentSpeed / 1024).toFixed(0))
    percentage.value = progress
  }

  window.DownloadPaused = () => {
    interruptedReason.value = "Paused"
  }

  window.DownloadInterrupted = (reason: string) => {
    interruptedReason.value = reason
  }
})

</script>

<template>
  <div>
    <div>当前版本号：{{ user.currentVersionRef }}</div>
    <div>最新版本号：{{ user.targetVersionRef }}</div>
    <n-progress type="line"
                :percentage="percentage"
                :status="status"
                processing/>
    <div>{{ speedVal }}kb/s</div>
    <div v-if="interruptedReason">下载暂停,原因是：</div>

  </div>
</template>

<style scoped>

</style>
