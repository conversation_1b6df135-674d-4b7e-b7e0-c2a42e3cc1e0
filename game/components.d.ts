/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AutoDoGem: typeof import('./src/components/auto-do-gem.vue')['default']
    AutoIdentify: typeof import('./src/components/auto-identify.vue')['default']
    AutoUseItem: typeof import('./src/components/auto-use-item.vue')['default']
    ButtonGroup: typeof import('./src/components/button-group/index.vue')['default']
    ClearBagArg: typeof import('./src/components/clear-bag-arg.vue')['default']
    CustomerSelect: typeof import('./src/components/customer-select.vue')['default']
    DailyTaskSelect: typeof import('./src/components/daily-task-select.vue')['default']
    DaMiJing: typeof import('./src/components/da-mi-jing.vue')['default']
    DialogApi: typeof import('./src/components/dialog-api.vue')['default']
    FullScreenIdentify: typeof import('./src/components/full-screen-identify.vue')['default']
    FullScreenTip: typeof import('./src/components/full-screen-tip.vue')['default']
    Function: typeof import('./src/components/function.vue')['default']
    KillBoss: typeof import('./src/components/kill-boss.vue')['default']
    MessageApi: typeof import('./src/components/message-api.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NGlobalStyle: typeof import('naive-ui')['NGlobalStyle']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NText: typeof import('naive-ui')['NText']
  }
}
