<template>
  <div id="gem-div">
    <n-spin :show="loading" :delay="1000">

      <n-space class="line-space">
        <n-text>剩余秘境币：</n-text>
        <n-text>{{ unGet }}</n-text>
      </n-space>
      <n-space class="line-space">
        <n-text>层数选择</n-text>
        <n-input-number v-model:value="floor" min="1" max="25" size="small"/>
      </n-space>
    </n-spin>
  </div>
</template>

<script setup lang="ts">


interface Props {
  callback: any,
}

const props = withDefaults(defineProps<Props>(), { callback: null })


const loading = ref(false)
const unGet = ref(0)
const floor = ref(5)
onMounted(() => {
  init()
})

const init = async () => {
  loading.value = true
  const info = await window.subWindow.mj.loadInfo()
  loading.value = false
  unGet.value = info.unGet


}

</script>

<style scoped>
input {
  width: 160px;
  background-color: transparent;
  border: 1px solid hsla(240, 1%, 72%, 0.25);
  color: white;
}

.line-space {
  width: 100%;
  margin-top: 4px;
}

.n-text {
  color: white;
  font-weight: bolder;
}
</style>
