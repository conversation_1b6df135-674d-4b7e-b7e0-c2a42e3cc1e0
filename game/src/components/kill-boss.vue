<template>
  <div id="gem-div">
    <n-space class="line-space">
      <n-input v-model:value="id" size="tiny" placeholder="请输入要击杀的boss"/>
    </n-space>
  </div>
</template>

<script setup lang="ts">

const id = ref("")

interface Props {
  call: any
}

const props = withDefaults(defineProps<Props>(), {call: null})

onMounted(() => {
  props.call.value = () => {
    let killId = Number(id.value)
    if (isNaN(killId) || killId <= 0) {
      return void window.$message.error("请输入正确的id")
    }
    window._world.toBattle(killId)
  }
})

</script>

<style scoped>
#gem-div {

}

input {
  width: 160px;
  background-color: transparent;
  border: 1px solid hsla(240, 1%, 72%, 0.25);
  color: white;
}

.line-space {
  width: 100%;
  margin-top: 4px;
}

.n-text {
  color: white;
  font-weight: bolder;
}

</style>
