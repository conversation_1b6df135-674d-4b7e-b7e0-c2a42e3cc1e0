<template>
  <div id="mask-top" v-if="scroll_top_vis"></div>
  <div id="identify-div" @scroll="onDivScroll">
    <n-space class="line-space">
      <n-text>装备名称</n-text>
      <n-text :style="{color:grade_color_txt}">{{ name_txt }}</n-text>
    </n-space>
    <n-space class="line-space">
      <n-text>方案选择</n-text>
      <select style="width: 160px" v-model="plan_selected" @change="onPlanChanged">
        <option v-for="item in plan_ary" :key="item.name" :value="item.name">{{ item.name }}</option>
      </select>
    </n-space>
    <n-space class="line-space">
      <n-text>类型选择</n-text>
      <select style="width: 160px" v-model="selected">
        <option v-for="item in options" :key="item.value" :value="item.value">{{ item.label }}</option>
      </select>
    </n-space>
    <n-space class="line-space">
      <n-text>鉴定间隔</n-text>
      <input style="width: 60px" placeholder="200毫秒" v-model="min"/>
      <input style="width: 60px" placeholder="300毫秒" v-model="max"/>
    </n-space>
    <n-space class="line-space" v-for="(ary,idx) in attrs">
      <n-text>&nbsp;&nbsp;属性&nbsp;{{ idx + 1 }}&nbsp;</n-text>
      <div style="width: 100%;text-align: left">
        <div v-for="(attr,aIdx) in ary">
          <select style="width: 130px;" v-model="attr.name" :disabled="attr.disable">
            <option v-for="item in attr_options" :key="item.label" :value="item.label">{{ item.label }}</option>
          </select>
          <input style="width: 40px;margin-left: 4px;" v-model="attr.minVal" :disabled="attr.disable"/>
          <n-checkbox style="margin-left: 4px;" v-model:checked="attr.percent" :disabled="attr.disable">
            <n-text>%</n-text>
          </n-checkbox>
          <n-button v-if="aIdx == 0 && !attr.disable" size="tiny" :secondary="true" :strong="true" type="success"
                    @click="handleAddAttr(ary)">或
          </n-button>
          <n-button v-if="aIdx > 0" size="tiny" :secondary="true" :strong="true" type="error"
                    @click="handleRemoveAttr(ary,aIdx)">删
          </n-button>
        </div>
      </div>
    </n-space>
    <n-space class="line-space" v-for="(desc, idx) in attr_txt_ary">
      <n-text>属性描述{{ idx + 1 }}</n-text>
      <n-text style="color:#00FF00;" v-html="desc"></n-text>
    </n-space>
    <n-space class="line-space">
      <n-text>方案名称</n-text>
      <input placeholder="注意：名称重复会替换" v-model="planName"/>
    </n-space>
    <n-space class="line-space">
      <n-text>方案导入</n-text>
      <input placeholder="在这里输入导入的字符串。" v-model="importPlanStr"/>
    </n-space>
    <n-space class="line-space">
      <n-button v-if="plan_selected && plan_selected != customer" size="tiny" :secondary="true" :strong="true"
                type="error"
                @click="handleDeleteSelectedPlan">删除当前选中的方案
      </n-button>
    </n-space>
    <n-space class="line-space">
      <n-text>&nbsp;</n-text>
    </n-space>
  </div>
  <div id="mask-bottom" v-if="scroll_bottom_vis"></div>
</template>

<script setup lang="ts">
import FullScreenIdentify from "@/components/full-screen-identify.vue";
import {NButton, NText} from "naive-ui";

interface Props {
  identify_callback: any,
  identify_plan_save_callback: any,
  identify_plan_import_callback: any,
  identify_stop_callback: any,
}

const props = withDefaults(defineProps<Props>(), {
  identify_callback: null,
  identify_plan_save_callback: null,
  identify_plan_import_callback: null,
  identify_stop_callback: null,
})

const options = ref([
  {label: "普通鉴定", value: 1, ary: [40001, 40000]},
  {label: "高级鉴定", value: 2, ary: [40023, 40022]},
  {label: "进阶鉴定", value: 3, ary: [40025, 40024]}
])
const selected = ref(-1)
const min = ref("")
const max = ref("")
const stop = ref("")

const scroll_top_vis = ref(false)
const scroll_bottom_vis = ref(true)

const name_txt = ref("")
const grade_color_txt = ref("")
const itemData = ref<any>({name: ""})
const attrs = ref<any[]>([])
const attr_txt_ary = ref<string[]>([])

const planName = ref("")
const importPlanStr = ref("")
const customer = "当前装备自定义"
const plan_selected = ref(customer)
const plan_ary = ref<{ name: string, data: string }[]>([{name: customer, data: ""}])

const getGradeColor = (grade: number) => {
  const color = eval(`Define.getGradeColor(${grade})`)
  return "#" + color.toString(16).toUpperCase().padStart(6, '0')
}

const handleAddAttr = (attr: any) => {
  attr.push({name: ref("任意属性"), minVal: 1, percent: ref(false), disable: ref(false)})
}
const handleRemoveAttr = (ary: [], idx: number) => {
  ary.splice(idx, 1)
}

const handleDeleteSelectedPlan = () => {
  plan_ary.value = plan_ary.value.filter(p => p.name != plan_selected.value)
  const ary = plan_ary.value.filter(p => p.name != customer)
  localStorage.setItem("identify_plan_data", JSON.stringify(ary))
  window.$message.success("方案删除成功！")
  plan_selected.value = customer
  loadEquipData()
}

const onPlanChanged = () => {
  if (plan_selected.value == "当前装备自定义") {
    loadEquipData()
  } else {
    loadPlanData()
  }
}
// 懒得对select  input做监听了，用伪循环来遍历
const lid = setInterval(() => {
  if (!attrs.value.length) {
    return
  }
  attr_txt_ary.value = []
  for (let i = 0; i < attrs.value.length; i++) {
    const ary = attrs.value[i]
    let str = ""
    for (let j = 0; j < ary.length; j++) {
      const attr = ary[j]
      if (j > 0) {
        str += `<p style="text-align: left;color: #c23616!important;">或者<p/>`
      }
      if (attr.name === "任意属性") {
        str = `<p style="text-align: left;text-decoration: line-through;">${attr.name}<p/>`
        break
      }
      str += `<p style="text-align: left">${attr.name}+${attr.minVal}${attr.percent ? "%" : ""} ${!attr.disable ? "" : ""}<p/>`
    }
    attr_txt_ary.value.push(str)
  }
  localStorage.setItem("identify-min", min.value)
  localStorage.setItem("identify-max", max.value)
}, 500)

const loadEquipData = () => {
  attrs.value = []
  const item = itemData.value;
  const types = ["power1", "power2", "power3", "bindPower1", "bindPower2"]
  const vals = ["powerValue1", "powerValue2", "powerValue3", "bindPowerValue1", "bindPowerValue2"]
  for (let i = 0; i < types.length; i++) {
    const type = types[i]
    const val = vals[i]
    if (item[type]) {
      const desc = eval(`Define.getPowerDesc(${item[type]}, ${item[val]})`)
      const detail = desc.split("+")
      let name = detail[0]
      let _v = item[val]
      if (name == "未鉴定属性") {
        _v = 1
        name = "任意属性"
      }
      if (!name) continue
      name = name.trim()
      let disable = type == "power3"
      attrs.value.push([
        {
          name: ref(name),
          minVal: ref(_v),
          percent: ref(desc.endsWith("%")),
          disable: ref(disable)
        }
      ])
    }
  }
}

const loadPlanData = () => {
  attrs.value = []
  const item = itemData.value;
  const plan = plan_ary.value.find(p => p.name == plan_selected.value)
  if (!plan) return void window.$message.error("方案出错了，请手动删除后再导入！")
  const data = JSON.parse(plan.data)
  for (const ary of data) {
    const _: any[] = []
    for (const attr of ary) {
      _.push({
        name: ref(attr.name),
        minVal: ref(attr.minVal),
        percent: ref(attr.percent),
        disable: ref(attr.disable)
      })
    }
    attrs.value.push(_)
  }
}

onMounted(() => {
  min.value = localStorage.getItem("identify-min") || ""
  max.value = localStorage.getItem("identify-max") || ""
  const plan_data_str = localStorage.getItem("identify_plan_data") || ""
  if (plan_data_str) {
    try {
      const plan_data = JSON.parse(plan_data_str)
      for (const data of plan_data) {
        plan_ary.value.push({name: data.name, data: data.data})
      }
    } catch (e) {
    }
  }
  itemData.value = eval(`PanelManager.getPanel(ForgeScene, !1).identifyPanel.item._itemData`)
  name_txt.value = itemData.value.name
  grade_color_txt.value = getGradeColor(itemData.value.grade)
  const _comboType = eval(`PanelManager.getPanel(ForgeScene, !1).identifyPanel._comboType`)
  if (!_comboType) {
    selected.value = itemData.value.reqLv >= 65 ? 2 : 1
  } else {
    selected.value = 3
  }
  loadEquipData()
  props.identify_plan_save_callback.value = () => {
    let name = planName.value.trim()
    if (!name) {
      name = plan_selected.value
    }
    if (!name) return void window.$message.error("请输入方案名称!")
    if (name == customer) return void window.$message.error("不可以使用这个名字!")
    const str = JSON.stringify(attrs.value)
    const idx = plan_ary.value.findIndex(p => p.name == name)
    if (idx == -1) {
      plan_ary.value.push({name: name, data: str})
      window.$message.success("方案保存成功!")
    } else {
      plan_ary.value[idx].data = str
      window.$message.success("方案替换成功!")
    }
    const ary = plan_ary.value.filter(p => p.name != customer)
    localStorage.setItem("identify_plan_data", JSON.stringify(ary))
  }

  props.identify_plan_import_callback.value = () => {
    let str = window.base64Decode(importPlanStr.value)
    if (!str) return void window.$message.error("请输入导入的字符串!")
    try {
      const data = JSON.parse(str)
      for (const _ of data) {
        const idx = plan_ary.value.findIndex(p => p.name == _.name)
        if (idx == -1) {
          plan_ary.value.push(_)
          continue
        }
        plan_ary.value[idx].data = _.data
      }
      window.$message.success("方案导入成功!")
      importPlanStr.value = ""
      planName.value = ""
      const ary = plan_ary.value.filter(p => p.name != customer)
      localStorage.setItem("identify_plan_data", JSON.stringify(ary))
    } catch (e) {
      window.$message.error("方案解析失败！")
    }
  }

  props.identify_callback.value = () => {
    const data = calculate()
    if (!data) return void window.$message.error("计算的属性列表为空？请反馈给开发者")
    const opt = options.value.find(o => o.value == selected.value)
    if (!opt) return void window.$message.error("卷轴列表为空？请反馈给开发者")
    let ele = document.getElementById("inner-record-text")
    if (ele) {
      ele.innerHTML = ""
    }
    record.value = ""
    //@ts-ignore
    window.onExecute = onExecute
    //@ts-ignore
    window.identifyPrintIndex = 0
    // @ts-ignore
    window.onIdentifyMatch = (cnt, data) => {
      if (cnt > 0) {
        let str = ""
        //@ts-ignore
        if (window.identifyPrintIndex < cnt) {
          //@ts-ignore
          window.identifyPrintIndex = cnt
          str += `<p style="color: #ff3838;font-size: 24px;">第${cnt}次: </p>`
        }
        const {attr, index} = data
        str += `<p style="margin-left: 12px;">属性${index + 1}: <span style="color:#00FF00;">${attr.name}+${attr.value}${attr.isPercent ? "%" : ""}</span></p>`
        record.value += str
      } else {
        record.value += data || ""
      }
      ele = document.getElementById("inner-record-text")
      if (ele) {
        ele.innerHTML = record.value
      }
      if (record_lock.value) {
        const btn = document.getElementById("inner-record-text-btn")
        if (btn) {
          btn.style.position = ""
          btn.style.bottom = ""
        }
        const full = document.getElementById("inner-record-text-body")
        if (full) {
          full.scrollTop = full.scrollHeight
        }
      } else {
        const btn = document.getElementById("inner-record-text-btn")
        if (btn) {
          btn.style.position = "fixed"
          btn.style.bottom = "20px"
        }
      }
    }
    let dt = 200
    if (min.value && max.value) {
      dt = Math.random() * (Number(max.value) - Number(min.value)) + Number(min.value)
      dt = Math.max(200, dt)
    }
    //@ts-ignore
    window.identify_temp_str = data;
    //@ts-ignore
    AutoIdentify.run = true;
    //@ts-ignore
    AutoIdentify.wait = dt;
    //@ts-ignore
    AutoIdentify.itemIdAry = opt.ary;
    //@ts-ignore
    AutoIdentify.check = window.identify_temp_str;
    //@ts-ignore
    AutoIdentify.start();
  }
  props.identify_stop_callback.value = () => {
  }
})

const record = ref<string>("")
const record_lock = ref(true)

const onExecute = () => {
  let dlg = document.getElementsByClassName("n-modal-container")
  if (dlg.length) {
    // @ts-ignore
    dlg[0].style.opacity = 0
  }
  record.value = ""
  let ele = document.getElementById("inner-record-text")
  if (ele) {
    ele.innerHTML = ""
  }
  window.showTip(h(FullScreenIdentify, {}, {
    content: () => h("div", {
      style: {maxHeight: "100%", overflowY: 'scroll'},
      id: "inner-record-text-body"
    }, {
      default: () => [
        h("div", {id: "inner-record-text", style: {width: "80%"}}, {default: () => ""}),
        h("div", {id: "inner-record-text-btn", style: {right: '10px', display: 'flex', flexDirection: 'column'}}, {
          default: () => [
            h(NButton, {
              type: 'success',
              size: 'tiny',
              style: {},
              onClick: () => {
                eval(`AutoIdentify.run = false;`)
                //@ts-ignore
                window.onExecute = null
                //@ts-ignore
                window.onIdentifyMatch = null
                if (dlg.length) {
                  // @ts-ignore
                  dlg[0].style.opacity = 255
                }
                window.hideTip();
              }
            }, {default: () => h('span', {style: {color: 'white'}}, {default: () => "停止or关闭"})}),
            h(NButton, {
              type: 'warning',
              size: 'tiny',
              style: {marginTop: "8px"},
              onClick: () => {
                record_lock.value = !record_lock.value
              }
            }, {default: () => h('span', {style: {color: 'white'}}, {default: () => record_lock.value ? "自由滚动" : "锁定列表"})}),
          ]
        }),
        h("div", {style: {width: "80%", height: "20px"}}, {}),
      ]
    })
  }))
}

const calculate = () => {
  const data: any[] = []
  if (!attrs.value || !attrs.value.length) return null
  attrs.value.forEach(ary => {
    let _ = ""
    for (const attr of ary) {
      // 有一条任意属性，那就没得必要了
      if (attr.name == "任意属性") {
        _ = "null;"
        break
      } else {
        _ += `${attr.name}|${attr.minVal}|${attr.percent ? 1 : 0}|0;`
      }
    }
    if (_ == "null;") {
      data.push(null)
      return
    }
    data.push({name: _})
  })
  return data
}
const onDivScroll = (evt: Event) => {
  // @ts-ignore
  scroll_top_vis.value = evt.target.scrollTop > 0
  // @ts-ignore
  scroll_bottom_vis.value = evt.target.scrollTop < evt.target.scrollHeight - evt.target.clientHeight
}
onUnmounted(() => {
  if (lid) clearInterval(lid)
})

const attr_options = ref([
  {label: "任意属性"},
  {label: "力量"},
  {label: "体质"},
  {label: "敏捷"},
  {label: "智力"},
  {label: "感知"},
  {label: "生命上限"},
  {label: "法力上限"},
  {label: "魔法穿透"},
  {label: "破甲"},
  {label: "劈砍攻击力"},
  {label: "穿刺攻击力"},
  {label: "魔法攻击力"},
  {label: "魔法命中"},
  {label: "洞察"},
  {label: "状态抵抗值"},
  {label: "无视状态抵抗"},
  {label: "命中"},
  {label: "出手速度"},
  {label: "免伤护盾"},
  {label: "无视伤害减免"},
  {label: "无视状态抵抗"},
  {label: "无视洞察"},
  {label: "无视格挡"},
  {label: "无视魔法反馈"},
  {label: "无视反击"},
  {label: "每回合自动恢复生命"},
  {label: "每回合自动恢复法力"},
  {label: "武器伤害、耐久"},
  {label: "攻击次数"},
  {label: "剑类武器伤害、耐久"},
  {label: "用剑时攻击次数"},
  {label: "刀类伤害、耐久"},
  {label: "用刀时攻击次数"},
  {label: "重型武器伤害、耐久"},
  {label: "用重型时攻击次数"},
  {label: "长柄武器伤害、耐久"},
  {label: "用长柄时攻击次数"},
  {label: "法杖武器伤害、耐久"},
  {label: "用法杖时攻击次数"},
  {label: "法器武器伤害、耐久"},
  {label: "用法器时攻击次数"},
  {label: "空手伤害"},
  {label: "空手时攻击次数"},
  {label: "远程武器伤害、耐久"},
  {label: "用远程武器时攻击次数"},
  {label: "火枪类武器伤害、耐久"},
  {label: "用火枪时攻击次数"},
  {label: "命中率"},
  {label: "躲闪"},
  {label: "致命率"},
  {label: "劈砍防御力"},
  {label: "穿刺防御力"},
  {label: "魔法防御力"},
  {label: "基础状态抵抗"},
  {label: "伤害减免"},
  {label: "格挡"},
  {label: "法力护盾"},
  {label: "反击"},
  {label: "魔法反馈"},
  {label: "生命吸收"},
  {label: "法力吸收"},
  {label: "强制命中率"},
  {label: "宠物伤害"},
  {label: "宠物生命上限"},
  {label: "宠物法力上限"},
  {label: "宠物力量"},
  {label: "宠物敏捷"},
  {label: "宠物智力"},
  {label: "宠物感知"},
])
</script>

<style scoped>

#identify-div {
  max-height: 12rem;
  overflow-y: scroll;
}

#mask-top {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform: rotate(180deg);
  transition: all 0.3s ease;
}

#mask-bottom {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s ease;
}

#mask-top:before {
  background-image: linear-gradient(180deg, rgba(233, 237, 247, 0), rgba(22, 23, 26, 0.98));
  content: "";
  height: 10px;
  opacity: 1;
  pointer-events: none;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

#mask-bottom:before {
  background-image: linear-gradient(180deg, rgba(233, 237, 247, 0), rgba(22, 23, 26, 0.98));
  content: "";
  height: 10px;
  opacity: 1;
  pointer-events: none;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

input {
  width: 160px;
  background-color: transparent;
  border: 1px solid hsla(240, 1%, 72%, 0.25);
  color: white;
}

.line-space {
  width: 100%;
  margin-top: 4px;
}

.n-text {
  color: white;
  font-weight: bolder;
}

</style>
