<template>
  <div id="gem-div">
    <n-space class="line-space">
      <n-text>物品选择</n-text>
      <select style="width: 160px" v-model="selected" @change="onSelectItem">
        <option v-for="item in options" :key="item.key" :value="item.value">{{ item.label }}</option>
      </select>
    </n-space>
    <n-space class="line-space">
      <n-text>使用数量</n-text>
      <n-input placeholder="不要超过持有数量" v-model:value="cnt" size="tiny"></n-input>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: darkred">如果要使用宠物经验书，先去打开宠物界面。</n-text>
    </n-space>
  </div>
</template>

<script setup lang="ts">


interface Props {
  callback: any,
  stopCallBack: any
}

const props = withDefaults(defineProps<Props>(), {callback: null, stopCallBack: null})

const options = ref([])
const selected = ref(-1)
const defaultOptions = {label: "背包没发现可使用道具", value: -1}
const cnt = ref("")

onMounted(() => {
  const items = eval("_world.extractCanUseItem()")
  if (items.length) {
    const ls = []
    for (const item of items) {
      const oldItem = ls.find(i => i && i.id == item.id)
      if (oldItem) {
        oldItem.quantity += item.quantity
        continue
      }
      ls.push({id: item.id, quantity: item.quantity, name: item.name})
    }
    ls.forEach(item => options.value.push({
      label: `${item.name} - ${item.quantity}个`,
      value: item.id,
      obj: item
    }))
    selected.value = options.value[0].value
    cnt.value = options.value[0].obj.quantity + ""
  } else {
    options.value.push(defaultOptions)
  }
  props.callback.value = () => {
    if (selected.value == -1) {
      return void window.$message.error("请选择物品")
    }
    const item = options.value.find((item) => item.value == selected.value)
    try {
      if (Number(cnt.value) <= 0 || Number(cnt.value) > item["quantity"]) {
        return void window.$message.error("数量不正确!")
      }
    } catch (e) {
      return void window.$message.error("输入的数值有误!")
    }
    window._world.useItemByName(item.obj, Number(cnt.value), props.stopCallBack.value)
    return true
  }
})

const onSelectItem = () => {
  cnt.value = -1
  options.value.forEach(item => {
    if (item.value == selected.value) {
      cnt.value = item.obj["quantity"] + ""
    }
  })
}

</script>

<style scoped>
#gem-div {

}

input {
  width: 160px;
  background-color: transparent;
  border: 1px solid hsla(240, 1%, 72%, 0.25);
  color: white;
}

.line-space {
  width: 100%;
  margin-top: 4px;
}

.n-text {
  font-weight: bolder;
}

</style>
