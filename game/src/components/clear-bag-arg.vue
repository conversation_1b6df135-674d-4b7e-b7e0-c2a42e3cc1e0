<template>
  <div id="gem-div">
    <n-space class="line-space">
      <n-text>物品名称</n-text>
      <n-input placeholder="多个用,隔开" size="tiny" v-model:value="itemName" clearable></n-input>
    </n-space>
    <n-space class="line-space" v-for="(arg,idx) in rubbishArgs">
      <n-checkbox v-model:checked="arg.selected">
        <n-text>出售<span :class="gradeTextColor[arg.grade]">{{ arg.desc }}</span>装备,等级小于</n-text>
      </n-checkbox>
      <n-input-number v-model:value="arg.level" :show-button="false" size="tiny" style="width: 34px"
                      :min="1" :max="99"/>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: #c23616;font-weight: bolder">勾选/取消参数，需要点设置才生效。</n-text>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: #c23616;font-weight: bolder">装备参数,排除了[潜能,时装,坐骑]类型.</n-text>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: #c23616;font-weight: bolder">添加、移除清理，需要输入对应的名字。</n-text>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: #c23616;font-weight: bolder">物品名字多个使用逗号隔开。</n-text>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: #c23616;font-weight: bolder">多多复制数据查看确认，避免搞错。</n-text>
    </n-space>
    <n-space class="line-space">
      <n-text style="color: #c23616;font-weight: bolder">名字不区分中英文的逗号括号。</n-text>
    </n-space>
  </div>
</template>

<script setup lang="ts">


import type {Ref} from "vue";

interface Props {
  call: Ref<null | ((type: number, $el?: any) => void)>,
}

const props = withDefaults(defineProps<Props>(), {call: null})

const itemName = ref("")
const rubbishArgs = ref<any>([])
const gradeTextColor = ['white-text', 'green-text', 'blue-text', 'orange-text', 'purple-text', 'red-text']

onMounted(() => {
  const str = localStorage.getItem("__rubbishArgs") || ""
  if (str) {
    rubbishArgs.value = JSON.parse(str)
  } else {
    rubbishArgs.value = [
      {grade: 0, desc: "白色", selected: false, level: 40},
      {grade: 1, desc: "绿色", selected: false, level: 40},
      {grade: 2, desc: "蓝色", selected: false, level: 40},
      {grade: 3, desc: "橙色", selected: false, level: 40},
    ]
  }
  props.call.value = (type: number, $el?: any) => {
    let name = itemName.value
    if (!name && type != 3) {
      const newStr = JSON.stringify(rubbishArgs.value)
      if (newStr == str) {
        return window.$message.error("请先填写物品名称")
      }
    }
    //@ts-ignore
    name = name.replaceAll("，", ",")
    switch (type) {
      case 1:
        const code = window._world.addRubbishName(name, rubbishArgs.value)
        if (code === 0) {
          return window.$message.error("背包中找不到要添加的物品")
        }
        window.$message.success("添加成功!")
        break
      case 2:
        window._world.removeRubbishName(name, rubbishArgs.value)
        window.$message.success("移除操作成功!")
        break
      case 3:
        let str = window._world.rubbishNameArray.join(",")
        !str && (str = "暂无数据。")
        window.copy(str, $el)
        break
    }

  }
})


</script>

<style scoped>
.white-text {
  color: #ffffff !important;
}

.green-text {
  color: #2ecc71 !important;
}

.blue-text {
  color: #3EB8FA !important;
}

.orange-text {
  color: #FFB302 !important;
}

.purple-text {
  color: #BE71F4 !important;
}

.red-text {
  color: #c23616 !important;
}
</style>
