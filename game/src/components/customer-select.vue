<template>
  <n-grid :x-gap="1" :y-gap="1" :cols="5" style="width: 90%;" :style="`--bg-color: ${color}`">
    <n-grid-item span="5">
      <n-data-table ref="tableRef" :columns="columns" :data="data" remote size="small" :loading="itemLoading"
        :max-height="142" :min-height="142" :render-cell="renderItemCell" :row-props="rowProps"
        :row-class-name="rowClassName" />
    </n-grid-item>

    <n-grid-item span="2">
      <n-text size="tiny" :style="{ fontSize: '12px', color: selectRow ? getGradeColor(selectRow['grade']) : 'white' }">
        {{ selectRow ? selectRow["name"] : "请选择道具" }}
      </n-text>
    </n-grid-item>
    <n-grid-item span="2">
      <n-select size="tiny" :options="types" v-model:value="type"></n-select>
    </n-grid-item>
    <n-grid-item>
      <n-button size="tiny" type="primary" :loading="rewardListLoading" @click="doItemList">请求</n-button>
    </n-grid-item>

    <n-grid-item span="5">
      <n-data-table v-if="itemListData.length > 0" :columns="itemListColumns" :data="itemListData" remote size="small"
        :max-height="142" :render-cell="renderItemCell" :row-props="itemRowProps" />
    </n-grid-item>

    <n-grid-item span="5" v-if="itemListData.length > 0">
      <n-text size="tiny" :style="{ fontSize: '12px', color: selectItem ? getGradeColor(selectItem['grade']) : 'white' }">
        {{ selectItem ? selectItem["name"] : "请选择道具" }}
      </n-text>
    </n-grid-item>
    <n-grid-item span="2" v-if="itemListData.length > 0">
      <n-select size="tiny" :options="types" v-model:value="type"></n-select>
    </n-grid-item>
    <n-grid-item span="2" v-if="itemListData.length > 0">
      <input :value="numValue" :placeholder="placeholderValue" @input="e => numValue = e.target.value.trim()"
        style="width: 120px;background-color: transparent;border: 1px solid hsla(240, 1%, 72%, 0.25);color: white;" />
    </n-grid-item>
    <n-grid-item span="1" v-if="itemListData.length > 0">
      <n-button size="tiny" type="primary" :loading="doItemSelectLoading" @click="doItemSelect">领取{{ numValue || 1 }}个
      </n-button>
    </n-grid-item>

  </n-grid>
</template>
<script setup lang="ts">

import { NButton, NInput, NSpace, NText } from "naive-ui";

interface Props {
  auto: boolean
}

const props = withDefaults(defineProps<Props>(), {})
const tableRef = ref(null)
const selectRow = ref(null)
const rowProps = (row: any) => {
  return {
    style: `font-size: 12px; color: white;`,
    onClick: () => {
      if (selectRow.value != null) {
        selectRow.value["selected"] = false
      }
      selectRow.value = row
      row["selected"] = true
      itemListData.value.length = 0
      const name = selectRow.value['name']
      let total = 0
      data.value.forEach(item => item.id == row.id && (total += item.quantity))
      if (total > 1) {
        placeholderValue.value = `1~${total}个`
      }
      if (name && name.indexOf("宠物蛋") > -1) {
        type.value = 2
      } else {
        type.value = 1
      }
    }
  }
}
const rowClassName = (row) => {
  return row.selected ? 'row-selected' : ''
}

const search = ref("")
const createColumns = () => {
  return [
    { title: () => 'id', key: 'id', width: 80 },
    { title: () => '数量', key: 'quantity', width: 45 },
    {
      key: 'name', title: () => h("input", {
        type: "text",
        style: {
          width: "120px",
          backgroundColor: "transparent",
          border: '1px solid hsla(240, 1%, 72%, 0.25)',
          color: 'white',
          placeholder: "搜索过滤"
        },
        value: search.value,
        onInput: (e) => {
          search.value = e.target.value.trim()
          if (!baseData.value.length) return
          data.value = baseData.value.filter(item => {
            return item.name.includes(search.value)
          })
          localStorage.setItem("customer-select-search", search.value)
        }
      })
    },
  ]
}

const columns = createColumns()
const data = ref<any[]>([])
const baseData = ref<any[]>([])

const type = ref(1)
const types = ref([
  { label: "道具", value: 1 },
  // { label: "宠物", value: 2 },
])

const itemLoading = ref(false)
const doItemLoading = () => {
  const xself = window.xself
  if (xself == null) {
    return void window.$message.error("玩家数据加载失败,请重启!")
  }
  itemLoading.value = true
  const bag = xself.bag
  data.value.length = 0
  baseData.value.length = 0
  for (let i = 30; i < bag.bagEnd; i++) {
    const item = bag.getItem(i)
    if (item == null) {
      continue
    }
    baseData.value.push({
      slotPos: item.slotPos,
      id: item.id,
      quantity: item.quantity,
      name: item.name,
      grade: item.grade,
      selected: false,
    })
  }
  data.value = baseData.value
  itemLoading.value = false
}

const rewardListLoading = ref(false)
const itemListColumns = ref([
  { title: '名称', key: 'name' },
  {
    title: '品质', key: 'name', width: 60, render: (row) => {
      switch (row.grade) {
        case 0:
          return h(NText, { style: { color: '#ffffff' }, depth: 3 }, { default: () => '白色' })
        case 1:
          return h(NText, { style: { color: '#2ecc71' }, depth: 3 }, { default: () => '绿色' })
        case 2:
          return h(NText, { style: { color: '#3EB8FA' }, depth: 3 }, { default: () => '蓝色' })
        case 3:
          return h(NText, { style: { color: '#FFB302' }, depth: 3 }, { default: () => '橙色' })
        case 4:
          return h(NText, { style: { color: '#BE71F4' }, depth: 3 }, { default: () => '紫色' })
        default:
          return h(NText, { style: { color: '#c23616' }, depth: 3 }, { default: () => '未知' })
      }
    }
  },
])
const selectItem = ref(null)

const renderItemCell = (value, row) => {
  return h(NText, { style: { color: getGradeColor(row.grade) }, depth: 3 }, { default: () => value })
}

const itemRowProps = (row) => {
  return {
    onClick: () => {
      if (selectItem.value != null) {
        selectItem.value["selected"] = false
      }
      selectItem.value = row
      row["selected"] = true
    }
  }
}

const getGradeColor = (grade) => {
  switch (grade) {
    case 0:
      return "#ffffff"
    case 1:
      return "#2ecc71"
    case 2:
      return "#3EB8FA"
    case 3:
      return "#FFB302"
    case 4:
      return "#BE71F4"
    default:
      return "#c23616"
  }
}
const itemListData = ref([])

const doItemList = () => {
  const item = selectRow.value
  if (item == null) {
    return void window.$message.error("请选择一个物品")
  }
  rewardListLoading.value = true
  // 注册一个超时兼容
  const timeout = setTimeout(() => {
    window.onGetItemList = null
    rewardListLoading.value = false
    window.$message.error("请求超时, 请重试 !")
  }, 10000)
  // 注入到window  给脚本回调
  window.onGetItemList = (items: any[], isPet: boolean) => {
    clearTimeout(timeout)
    rewardListLoading.value = false
    if (!items || items.length == 0) {
      return void window.$message.error("这个道具没有奖池信息!")
    }
    itemListData.value.length = 0;
    items.forEach(item => {
      if (item.grade == 4 && isPet) return
      itemListData.value.push({
        id: item.id,
        name: item.name,
        type: item.type,
        info: item.info,
        grade: item.grade,
      })
    })
    rewardListLoading.value = false
    window.onGetItemList = null
    sendPoolReq.value = true
  }
  const { slotPos, id } = item
  const bagItem = window.xself.bag.getItem(slotPos)
  if (bagItem == null) {
    return void window.$message.error("背包中对应的物品已经不存在，请重新打开面板.")
  }
  if (type.value == 1) {
    window.doGetMountGuideItemList(bagItem)
  } else {
    window.PetGuide.doPetGuideList(bagItem)
  }
}

const sendPoolReq = ref(false)
const numValue = ref("")
const placeholderValue = ref("默认1个")
const doItemSelectLoading = ref(false)
const doItemSelect = () => {
  const item = selectItem.value
  if (item == null) {
    return void window.$message.error("请选择要自选的物品")
  }

  doItemSelectLoading.value = true
  let cnt = 1
  if (numValue.value != "") {
    cnt = parseInt(numValue.value)
  }
  let max = 0
  data.value.forEach(item => item.id == item.id && (max += item.quantity))
  cnt = Math.min(cnt, max)
  if (cnt == 1) {
    // 发一次奖池请求
    if (!sendPoolReq.value) {
      const { slotPos, id } = selectRow.value
      const bagItem = window.xself.bag.getItem(slotPos)
      if (bagItem == null) {
        return void window.$message.error("背包中对应的物品已经不存在，请重新打开面板.")
      }
      if (type.value == 1) {
        window.doGetMountGuideItemList(bagItem)
      } else {
        window.PetGuide.doPetGuideList(bagItem)
      }
    }
    // 再发领取请求
    // 注册一个超时兼容
    const timeout = setTimeout(() => {
      window.onSelectItemDone = null
      doItemSelectLoading.value = false
      window.$message.error("请求超时, 请重试 !")
    }, 10000)

    window.onSelectItemDone = (get: boolean) => {
      clearTimeout(timeout)
      doItemSelectLoading.value = false
      if (!get) {
        return void window.$message.error("领取失败,请重试!")
      }
      selectRow.value['quantity'] -= 1
      window.$message.success("领取成功,整理背包后可见.")
      sendPoolReq.value = false
      window.onSelectItemDone = null
    }
    const { slotPos, id } = selectRow.value
    const bagItem = window.xself.bag.getItem(slotPos)
    if (bagItem == null) {
      return void window.$message.error("背包中对应的物品已经不存在，请重新打开面板.")
    }
    if (type.value == 1) {
      window.doMountGuideSelect(slotPos, id, item['id'])
    } else {
      window.PetGuide.doPetGuideSelect(slotPos, id, item['id'])
    }
    return
  }

  // 批量领取
  const sid = setInterval(() => {
    if (cnt <= 0) {
      clearInterval(sid)
      window.xself.bag.refreshBag()
      return window.$dialog.info({ title: "提示", content: "领取完成!" })
    }
    let { id } = selectRow.value
    const bagItem = window.xself.bag.getItemByID(id)
    if (bagItem == null) {
      return void window.$message.error("背包中对应的物品已经不存在，请重新打开面板.")
    }
    if (type.value == 1) {
      window.doGetMountGuideItemList(bagItem)
    } else {
      window.PetGuide.doPetGuideList(bagItem)
    }
    window.onSelectItemDone = (get: boolean) => {
      if (get) {
        const item = data.value.find(item => item.id == bagItem.id && item.slotPos == bagItem.slotPos)
        item && (item.quantity -= 1)
      }
      window.onSelectItemDone = null
    }
    if (type.value == 1) {
      window.doMountGuideSelect(bagItem.slotPos, id, item['id'])
    } else {
      window.PetGuide.doPetGuideSelect(bagItem.slotPos, id, item['id'])
    }
    cnt -= 1;
  }, 500)
}

onMounted(() => {
  search.value = localStorage.getItem("customer-select-search") || ""
  doItemLoading()
  eval("window.MountGuide.doGetMountGuideItemList=function(e){if(!e)return false;return new Promise((resolve,reject)=>{var i=new nato.Message(ProtocolDefine.CG_GET_RIDE);i.putShort(e.slotPos);i.putInt(e.id);PanelManager.openWaitForServer();_world.syncMsg(i,(n)=>{PanelManager.closeWaitForServer();i=n.getByte();if(0>i){window.onGetItemList&&window.onGetItemList(null,false);return resolve(false)}var o=n.getByte(),a=new MountGuide();a.itemid=e.id;a.itemslotPos=e.slotPos;a.vItemList=[];for(var r=0;o>r;r++){var s=new ItemData;ItemData.fromBytesAtts2(s,n),s.durability=s.durMax,a.vItemList.push(s)}a.vItemList.forEach(i=>console.log(i.name,i.id,i.grade));window.onGetItemList&&window.onGetItemList(a.vItemList,false);resolve(true)})})};")
  eval("window.doGetMountGuideItemList = MountGuide.doGetMountGuideItemList;")
  eval("window.doMountGuideSelect=async(slotPos,id,itemId)=>{const r=await new Promise((resolve,reject)=>{MountGuide.doMountGuideSelect(slotPos,id,itemId,resolve)});window.onSelectItemDone&&window.onSelectItemDone(!!r)};")
  eval("PlayerBag.prototype.getItemByID=function(id){for(var n=null,i=30;i<=this.bagEnd;i++){var o=this.getItem(i);if(null!=o&&o.id==id)return o}return n};")
})
const color = ref("red")

</script>
<style>
.n-data-table-th__title {
  font-size: 12px !important;
}

.bag-clear-span-text {
  color: #95a5a6 !important;
  font-size: 14px !important;
}

.white-text {
  color: #ffffff !important;
}

.green-text {
  color: #2ecc71 !important;
}

.blue-text {
  color: #3EB8FA !important;
}

.orange-text {
  color: #FFB302 !important;
}

.purple-text {
  color: #BE71F4 !important;
}

.red-text {
  color: #c23616 !important;
}

.time-text {
  color: #f7eead !important;
}

.row-selected {
  background-color: red !important
}

.n-data-table-th__title {
  color: white !important;
}

.n-data-table .n-data-table-table tr:hover>td {
  background-color: rgba(255, 0, 0, 0.4) !important
}

.n-data-table-table {
  background-color: transparent !important;
}

.n-data-table-thead {
  background-color: transparent !important;
}

.n-data-table-tr {
  background-color: transparent !important;
}

.n-data-table-th {
  background-color: transparent !important;
}

.n-data-table-tbody {
  background-color: transparent !important;
}

.n-data-table-td {
  background-color: transparent !important;
  color: white !important;
}
</style>
