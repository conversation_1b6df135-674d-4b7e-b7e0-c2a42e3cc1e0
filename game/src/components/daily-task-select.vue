<template>
  <n-space vertical style="line-height:12px;">
    <n-text style="font-weight: bolder;font-size: 10px;color: red;"> _________砖佬定制版本,限时开放测试_________ </n-text>
    <n-text style="font-weight: bolder;font-size: 10px;color: red;"> 注意背包容量,清理参数,野外修理,指令书 </n-text>
    <n-text style="font-weight: bolder;font-size: 10px;color: red;"> 不能挂机过的不要勾，部分副本要放够指令 </n-text>
    <n-text style="font-weight: bolder;font-size: 10px;color: red;"> [功能测试中] </n-text>
    <n-data-table v-if="loading" :columns="columns" :data="empty" :bordered="false" :flex-height="true"
      :loading="loading" />
    <n-grid v-else :x-gap="1" :y-gap="1" :cols="2" style="width: 96%;">
      <n-grid-item v-for="(item, index) in array">
        <n-space>
          <n-checkbox :disabled="getLevel() < item.level || item.status == 2" v-model:checked="item.check"
            :on-update:checked="(v: boolean) => { item.check = v; set() }" size="small">
            <template #default>
              <span :style="{
                color: getLevel() < item.level || item.status == 2 ? 'red' : 'white',
                fontWeight: 'bolder',
                textDecoration: getLevel() < item.level || item.status == 2 ? 'line-through' : 'none',
              }">{{ item.name }}</span>
              <span style="font-size:5px">{{ `(${item.level})` }}</span>
            </template>
          </n-checkbox>
        </n-space>
      </n-grid-item>
    </n-grid>
  </n-space>
</template>

<script setup lang="ts">


interface Props {
  callback: any,
}

const props = withDefaults(defineProps<Props>(), { callback: null })
const columns: [] = [];
const empty = ref([])

type dailyTaskObj = {
  name: string,
  level: number,
  idAry: number[],
  check: boolean,
  status: number,
  book: number,
}

const array = ref<dailyTaskObj[]>([])
const loading = ref(false)
onMounted(() => {
  const old = get();
  array.value = [];

  const data = eval(`oneKeyTask.parseData()`)
  loading.value = false;
  if (!data) {
    return;
  }
  for (const task of data) {
    const o = old.find((i: dailyTaskObj) => i.name == task.name);
    task.check = o && o.check && getLevel() >= task["level"] && task.status != 2
    array.value.push(task)
  }

  // 返回选择的情况数据
  props.callback.value = () => get();
})

const get = () => {
  localStorage.removeItem("dailyTaskSelectInfo")
  const info = localStorage.getItem("_dailyTaskSelectInfo")
  return info ? JSON.parse(info) : [];
}

const set = () => {
  localStorage.setItem("_dailyTaskSelectInfo", JSON.stringify(array.value))
}

const getLevel = () => window.xself.level

</script>

<style></style>
