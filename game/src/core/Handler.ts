export default class Handler {
    private _context: any
    private _method: Function | null = null
    private args: any[] | null = null
    private _once: boolean = false
    private _id: number = 0

    public static _guid = 1

    public doExec(ctx: any, method: Function, args: any[]) {
        let result = null;
        try {
            result = method.apply(ctx, args)
        } catch (a) {
            args ? result = method(...args) : result = method()
        }
        return result
    }

    public get isOnce() {
        return this._once || !this.context
    }

    /**
     * 执行
     * @param params 需要传递的参数
     */
    public exec(params?: any) {
        if (!this._method) return null
        const tid = this._id
        const noParams = null == params
        const args = noParams ? this.args : (this.args || params.unshift) && this.args ? this.args.concat(params) : params;
        const result = this.doExec(this._context, this._method, args);
        if (this._id === tid && this.isOnce) {

        }
        return result
    }

    public set(id: number, ctx: any, cb: any, args: any, once: boolean) {
        let a = this;
        a._id = id
        a._context = ctx
        a._method = cb
        a.args = args
        a._once = once
        return a
    }

    public get method() {
        return this._method
    }

    public get context() {
        return this._context
    }

    public static alloc(ctx: any, cb: any, args?: any[], once?: boolean): Handler {
        return new Handler().set(Handler._guid++, ctx, cb, args ? args : [], !!once)
    }
}
//@ts-ignore
window["Handler"] = Handler
